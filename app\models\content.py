"""
Content models for PDF documents and URL content.
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from dataclasses import dataclass

@dataclass
class PDFDocument:
    """Model for PDF documents stored in the system."""
    id: Optional[int] = None
    filename: str = ""
    original_filename: str = ""
    category: str = ""
    upload_date: datetime = None
    source_url_id: Optional[int] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.upload_date is None:
            self.upload_date = datetime.now()
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class URLContent:
    """Model for URL content scraped from web sources."""
    id: Optional[int] = None
    source_url_id: int = 0
    content_type: str = "text"  # text, image, link
    content: str = ""
    content_order: int = 0
    metadata: Optional[Dict[str, Any]] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class SourceURL:
    """Model for source URLs that content is scraped from."""
    id: Optional[int] = None
    url: str = ""
    title: Optional[str] = None
    description: Optional[str] = None
    last_scraped: Optional[datetime] = None
    last_updated: Optional[datetime] = None
    status: str = "active"  # active, archived, error
    error_message: Optional[str] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class CoverImage:
    """Model for cover images associated with PDF documents."""
    id: Optional[int] = None
    pdf_document_id: int = 0
    image_path: str = ""
    image_url: str = ""
    source: str = "pdf_first_page"  # pdf_first_page, pdf_internal, url, default
    description: Optional[str] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now() 