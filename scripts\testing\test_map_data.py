#!/usr/bin/env python3
"""
Test script to verify map visualization data processing
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

def test_map_data_processing():
    """Test the data processing logic used by the analytics dashboard."""
    print("🗺️  Testing Map Data Processing")
    print("=" * 40)
    
    try:
        from app.services.geo_analytics import get_geoip_data
        
        # Get geolocation data
        geoip_data = get_geoip_data()
        print(f"✅ Retrieved {len(geoip_data)} geolocation records")
        
        if not geoip_data:
            print("❌ No geolocation data found")
            return False
        
        # Process data like the analytics dashboard does
        location_counts = {}
        
        for entry in geoip_data:
            if entry.get('latitude') and entry.get('longitude'):
                location_key = f"{entry['latitude']},{entry['longitude']}"
                
                if location_key not in location_counts:
                    location_counts[location_key] = {
                        'latitude': entry['latitude'],
                        'longitude': entry['longitude'],
                        'city': entry['city'],
                        'country': entry['country'],
                        'count': 0,
                        'visitors': set(),
                        'client_names': set()
                    }
                
                # Count unique visitors
                if entry.get('device_fingerprint'):
                    location_counts[location_key]['visitors'].add(entry['device_fingerprint'])
                
                # Collect client names
                if entry.get('client_name'):
                    location_counts[location_key]['client_names'].add(entry['client_name'])
                
                location_counts[location_key]['count'] += 1
        
        print(f"✅ Processed into {len(location_counts)} unique locations")
        print()
        print("📍 Sample locations for map:")
        
        # Show top 10 locations by visitor count
        sorted_locations = sorted(
            location_counts.items(), 
            key=lambda x: len(x[1]['visitors']), 
            reverse=True
        )
        
        for i, (key, data) in enumerate(sorted_locations[:10]):
            visitors = len(data['visitors'])
            total_visits = data['count']
            client_names = list(data['client_names'])[:3]  # Show first 3 client names
            client_str = ', '.join(client_names) if client_names else 'Anonymous'
            
            print(f"   {i+1:2d}. {data['city']}, {data['country']}")
            print(f"       📊 {visitors} visitors, {total_visits} total visits")
            print(f"       👥 Clients: {client_str}")
            print(f"       📍 Coordinates: {data['latitude']}, {data['longitude']}")
            print()
        
        # Test data format for frontend
        location_data = []
        for location_info in location_counts.values():
            location_data.append({
                'latitude': location_info['latitude'],
                'longitude': location_info['longitude'],
                'city': location_info['city'],
                'region': location_info.get('region', ''),
                'country': location_info['country'],
                'count': len(location_info['visitors']),
                'total_visits': location_info['count'],
                'client_names': list(location_info['client_names'])[:5]
            })
        
        print(f"✅ Generated {len(location_data)} location markers for map")
        print(f"📊 Total unique visitors across all locations: {sum(len(loc['visitors']) for loc in location_counts.values())}")
        print(f"📊 Total visits across all locations: {sum(loc['count'] for loc in location_counts.values())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing map data: {str(e)}")
        return False

def test_analytics_dashboard_integration():
    """Test if the analytics dashboard can access the data."""
    print("\n🔗 Testing Analytics Dashboard Integration")
    print("=" * 45)
    
    try:
        # Simulate the analytics dashboard data retrieval
        from app.utils.database import get_analytics
        from app.services.geo_analytics import get_geoip_data
        
        # Get analytics data (for other dashboard components)
        analytics = get_analytics()
        print(f"✅ Retrieved {len(analytics)} analytics records")
        
        # Get geoip data (for map)
        geoip_data = get_geoip_data()
        print(f"✅ Retrieved {len(geoip_data)} geoip records")
        
        # Test date filtering
        geoip_filtered = get_geoip_data(start_date="2024-01-01")
        print(f"✅ Date filtering works: {len(geoip_filtered)} records since 2024-01-01")
        
        print("✅ Analytics dashboard integration ready")
        return True
        
    except Exception as e:
        print(f"❌ Error testing dashboard integration: {str(e)}")
        return False

def main():
    """Run all map visualization tests."""
    print("🗺️  Map Visualization Test Suite")
    print("=" * 50)
    
    results = {
        'data_processing': test_map_data_processing(),
        'dashboard_integration': test_analytics_dashboard_integration()
    }
    
    print("\n" + "=" * 50)
    print("📋 TEST SUMMARY")
    print("=" * 50)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name.upper().replace('_', ' ')}: {status}")
    
    passed_tests = sum(1 for result in results.values() if result)
    total_tests = len(results)
    
    print(f"\n📊 Overall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 Map visualization is ready!")
        print("💡 Visit http://localhost:8080/admin/analytics to see the map")
    else:
        print("⚠️  Some tests failed. Check the errors above.")

if __name__ == "__main__":
    main()
