"""
Greeting models for templates and analytics.
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from dataclasses import dataclass
import json

@dataclass
class GreetingTemplate:
    """Model for greeting templates."""
    id: Optional[int] = None
    template_type: str = "welcome"  # welcome, response, return_user, time_based
    greeting_text: str = ""
    context_conditions: Optional[Dict[str, Any]] = None
    is_active: bool = True
    weight: int = 1
    created_at: datetime = None
    updated_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
        if isinstance(self.context_conditions, str):
            try:
                self.context_conditions = json.loads(self.context_conditions)
            except json.JSONDecodeError:
                self.context_conditions = {}

@dataclass
class UserGreetingPreference:
    """Model for user greeting preferences."""
    user_id: int = 0
    preferred_greeting_style: str = "friendly"
    last_greeting_used: Optional[int] = None
    greeting_frequency: str = "every_response"
    
    def __post_init__(self):
        pass

@dataclass
class GreetingAnalytics:
    """Model for greeting analytics."""
    id: Optional[int] = None
    session_id: str = ""
    client_name: str = ""
    greeting_template_id: Optional[int] = None
    greeting_type: str = ""
    timestamp: datetime = None
    user_response_time: Optional[float] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.created_at is None:
            self.created_at = datetime.now() 