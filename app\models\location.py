"""
Location models for extracted locations and geocoding data.
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from dataclasses import dataclass

@dataclass
class ExtractedLocation:
    """Model for extracted locations from documents."""
    id: Optional[int] = None
    location_text: str = ""
    location_type: str = "place_name"  # place_name, address, coordinates, landmark, region, municipality, city, barangay
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    confidence_score: float = 0.0
    context_snippet: Optional[str] = None
    geocoded_address: Optional[str] = None
    country: Optional[str] = None
    region: Optional[str] = None
    city: Optional[str] = None
    municipality: Optional[str] = None
    barangay: Optional[str] = None
    administrative_level: Optional[str] = None
    created_at: datetime = None
    updated_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()

@dataclass
class LocationSource:
    """Model for location sources (where locations were extracted from)."""
    id: Optional[int] = None
    location_id: int = 0
    source_type: str = "pdf_document"  # pdf_document, chat_message, url_content
    source_id: int = 0
    page_number: Optional[int] = None
    extraction_method: str = "ner"  # ner, regex, manual
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class GeocodingCache:
    """Model for cached geocoding results."""
    id: Optional[int] = None
    location_query: str = ""
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    formatted_address: Optional[str] = None
    geocoding_service: str = "nominatim"
    confidence_score: float = 0.0
    cached_at: datetime = None
    expires_at: Optional[datetime] = None
    status: str = "success"  # success, failed, partial
    country: Optional[str] = None
    region: Optional[str] = None
    city: Optional[str] = None
    
    def __post_init__(self):
        if self.cached_at is None:
            self.cached_at = datetime.now() 