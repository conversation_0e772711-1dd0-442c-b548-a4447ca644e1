#!/usr/bin/env python3
"""
Test script to verify the greeting system is working properly.
This script tests both the backend greeting service and the API endpoint.
"""

import sys
import os
import requests
import json

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_greeting_service():
    """Test the GreetingManager service directly."""
    print("Testing GreetingManager service...")
    
    try:
        from app.services.greeting_service import GreetingManager
        
        # Initialize greeting manager
        greeting_manager = GreetingManager()
        
        # Test getting a contextual greeting
        context = {
            'device_fingerprint': 'test-device-123',
            'time_of_day': 'morning',
            'is_new_session': True
        }
        
        greeting_data = greeting_manager.get_contextual_greeting('TestUser', context)
        
        print(f"✓ Greeting service working!")
        print(f"  Greeting: {greeting_data['greeting']}")
        print(f"  Source: {greeting_data.get('source', 'unknown')}")
        print(f"  Template Type: {greeting_data.get('template_type', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"✗ Greeting service failed: {str(e)}")
        return False

def test_greeting_api():
    """Test the greeting API endpoint."""
    print("\nTesting greeting API endpoint...")
    
    try:
        # Test data
        test_data = {
            'client_name': 'TestUser',
            'context': {
                'device_fingerprint': 'test-device-123',
                'time_of_day': 'afternoon',
                'is_new_session': True
            }
        }
        
        # Make request to the API
        response = requests.post(
            'http://localhost:5000/api/greeting',
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✓ API endpoint working!")
                print(f"  Greeting: {result['greeting']}")
                print(f"  Template ID: {result.get('greeting_data', {}).get('template_id', 'unknown')}")
                return True
            else:
                print(f"✗ API returned error: {result.get('error', 'unknown error')}")
                return False
        else:
            print(f"✗ API request failed with status {response.status_code}")
            print(f"  Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("✗ Could not connect to the application. Make sure it's running on localhost:5000")
        return False
    except Exception as e:
        print(f"✗ API test failed: {str(e)}")
        return False

def test_database_templates():
    """Test if greeting templates exist in the database."""
    print("\nTesting database greeting templates...")
    
    try:
        from app.services.greeting_service import GreetingManager
        
        greeting_manager = GreetingManager()
        templates = greeting_manager.get_greeting_templates()
        
        if templates:
            print(f"✓ Found {len(templates)} greeting templates in database")
            
            # Show a few examples
            for i, template in enumerate(templates[:3]):
                print(f"  Template {i+1}: {template.get('template_type', 'unknown')} - {template.get('greeting_text', 'no text')[:50]}...")
            
            return True
        else:
            print("✗ No greeting templates found in database")
            return False
            
    except Exception as e:
        print(f"✗ Database test failed: {str(e)}")
        return False

def main():
    """Run all tests."""
    print("=== Greeting System Test ===\n")
    
    # Test the service layer
    service_ok = test_greeting_service()
    
    # Test database templates
    db_ok = test_database_templates()
    
    # Test the API endpoint
    api_ok = test_greeting_api()
    
    print("\n=== Test Results ===")
    print(f"Greeting Service: {'✓ PASS' if service_ok else '✗ FAIL'}")
    print(f"Database Templates: {'✓ PASS' if db_ok else '✗ FAIL'}")
    print(f"API Endpoint: {'✓ PASS' if api_ok else '✗ FAIL'}")
    
    if service_ok and db_ok:
        print("\n✓ Backend greeting system is working correctly!")
        if not api_ok:
            print("⚠ API test failed - make sure the Flask app is running")
    else:
        print("\n✗ Backend greeting system has issues that need to be fixed")
    
    return service_ok and db_ok

if __name__ == '__main__':
    main()
