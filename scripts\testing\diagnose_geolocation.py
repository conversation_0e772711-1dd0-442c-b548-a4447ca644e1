#!/usr/bin/env python3
"""
Geolocation Diagnostic Script for AI Analytics System

This script diagnoses and tests the user location mapping functionality
to identify issues with MaxMind integration, IP capture, and map visualization.
"""

import os
import sys
import sqlite3
import requests
import json
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

def test_environment_config():
    """Test environment configuration for geolocation."""
    print("=== ENVIRONMENT CONFIGURATION TEST ===")
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    config_items = [
        ('GEOLOCATION_ENABLED', os.getenv('GEOLOCATION_ENABLED')),
        ('MAXMIND_ACCOUNT_ID', os.getenv('MAXMIND_ACCOUNT_ID')),
        ('MAXMIND_LICENSE_KEY', os.getenv('MAXMIND_LICENSE_KEY')),
        ('MAXMIND_SERVICE', os.getenv('MAXMIND_SERVICE')),
        ('DEV_MODE', os.getenv('DEV_MODE')),
        ('TEST_IP', os.getenv('TEST_IP')),
        ('PRIVATE_IP_HANDLING', os.getenv('PRIVATE_IP_HANDLING')),
    ]
    
    for key, value in config_items:
        if key in ['MAXMIND_ACCOUNT_ID', 'MAXMIND_LICENSE_KEY'] and value:
            # Mask sensitive values
            masked_value = value[:4] + '*' * (len(value) - 4) if len(value) > 4 else '****'
            print(f"  {key}: {masked_value}")
        else:
            print(f"  {key}: {value}")
    
    # Check for missing critical configuration
    missing_config = []
    if not os.getenv('MAXMIND_ACCOUNT_ID') or os.getenv('MAXMIND_ACCOUNT_ID') == 'your_account_id_here':
        missing_config.append('MAXMIND_ACCOUNT_ID')
    if not os.getenv('MAXMIND_LICENSE_KEY') or os.getenv('MAXMIND_LICENSE_KEY') == 'your_license_key_here':
        missing_config.append('MAXMIND_LICENSE_KEY')
    
    if missing_config:
        print(f"  ❌ MISSING CONFIGURATION: {', '.join(missing_config)}")
        print("  📝 Please update .env file with valid MaxMind credentials")
        return False
    else:
        print("  ✅ All required configuration present")
        return True

def test_maxmind_service():
    """Test MaxMind GeoIP service connectivity."""
    print("\n=== MAXMIND SERVICE TEST ===")
    
    try:
        from app.services.geo_service import get_geolocation_data, test_geolocation
        
        # Test with a known public IP (Google DNS)
        test_ip = "*******"
        print(f"  Testing with IP: {test_ip}")
        
        result = test_geolocation(test_ip)
        
        if result.get('latitude') and result.get('longitude'):
            print(f"  ✅ MaxMind service working")
            print(f"     Location: {result.get('city', 'Unknown')}, {result.get('country', 'Unknown')}")
            print(f"     Coordinates: {result.get('latitude')}, {result.get('longitude')}")
            return True
        else:
            print(f"  ❌ MaxMind service not returning location data")
            print(f"     Result: {result}")
            return False
            
    except Exception as e:
        print(f"  ❌ MaxMind service error: {str(e)}")
        return False

def test_database_structure():
    """Test database structure for location data storage."""
    print("\n=== DATABASE STRUCTURE TEST ===")

    databases = [
        ('chat_history.db', ['geoip_analytics', 'chat_analytics']),
        ('erdb_main.db', ['extracted_locations', 'location_sources'])
    ]

    all_tables_ok = True

    for db_name, expected_tables in databases:
        if not os.path.exists(db_name):
            print(f"  ❌ Database {db_name} not found")
            all_tables_ok = False
            continue

        print(f"  📊 Checking {db_name}:")

        try:
            conn = sqlite3.connect(db_name)
            cursor = conn.cursor()

            # Check if tables exist
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            existing_tables = [row[0] for row in cursor.fetchall()]

            for table in expected_tables:
                if table in existing_tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    print(f"    ✅ {table}: {count} records")

                    # Check for location data in chat_analytics
                    if table == 'chat_analytics':
                        cursor.execute("SELECT COUNT(*) FROM chat_analytics WHERE latitude IS NOT NULL")
                        location_count = cursor.fetchone()[0]
                        print(f"       📍 Records with location data: {location_count}")

                        if location_count > 0:
                            cursor.execute("SELECT DISTINCT city, country FROM chat_analytics WHERE latitude IS NOT NULL LIMIT 5")
                            locations = cursor.fetchall()
                            print(f"       🌍 Sample locations: {locations}")
                else:
                    print(f"    ❌ {table}: Table missing")
                    all_tables_ok = False

            conn.close()

        except Exception as e:
            print(f"    ❌ Error checking {db_name}: {str(e)}")
            all_tables_ok = False

    return all_tables_ok

def test_ip_capture():
    """Test IP address capture functionality."""
    print("\n=== IP CAPTURE TEST ===")
    
    try:
        from app.services.geo_service import get_client_ip, get_location_for_analytics
        
        # Test IP detection (this will use development mode settings)
        print("  Testing IP detection...")
        
        # Since we're not in a Flask request context, we'll simulate
        print("  📝 Note: Running outside Flask request context")
        print("  📝 In actual application, IP would be captured from request.remote_addr")
        
        # Test the location analytics function
        ip, city, region, country, lat, lon = get_location_for_analytics()
        print(f"  IP: {ip}")
        print(f"  Location: {city}, {region}, {country}")
        print(f"  Coordinates: {lat}, {lon}")
        
        if lat and lon:
            print("  ✅ Location data available")
            return True
        else:
            print("  ❌ No location coordinates available")
            return False
            
    except Exception as e:
        print(f"  ❌ IP capture error: {str(e)}")
        return False

def test_analytics_integration():
    """Test analytics dashboard integration."""
    print("\n=== ANALYTICS INTEGRATION TEST ===")
    
    try:
        from app.utils.database import get_analytics
        
        # Get recent analytics data
        analytics = get_analytics()
        
        print(f"  📊 Total analytics records: {len(analytics)}")
        
        # Check for location data in analytics
        location_records = [a for a in analytics if a.get('latitude') and a.get('longitude')]
        print(f"  📍 Records with location data: {len(location_records)}")
        
        if location_records:
            # Show sample locations
            sample_locations = set()
            for record in location_records[:10]:
                location = f"{record.get('city', 'Unknown')}, {record.get('country', 'Unknown')}"
                sample_locations.add(location)
            
            print(f"  🌍 Sample locations: {list(sample_locations)}")
            print("  ✅ Analytics integration working")
            return True
        else:
            print("  ❌ No location data in analytics")
            return False
            
    except Exception as e:
        print(f"  ❌ Analytics integration error: {str(e)}")
        return False

def generate_test_data():
    """Generate test geolocation data for development."""
    print("\n=== GENERATING TEST DATA ===")
    
    try:
        from app.services.geo_analytics import save_geoip_data
        import uuid
        
        # Test IPs with known locations
        test_data = [
            ("*******", "Mountain View", "California", "United States", 37.4056, -122.0775),
            ("*******", "San Francisco", "California", "United States", 37.7749, -122.4194),
            ("**************", "San Francisco", "California", "United States", 37.7749, -122.4194),
        ]
        
        print("  📝 Adding test geolocation data...")
        
        for ip, city, region, country, lat, lon in test_data:
            device_fp = str(uuid.uuid4())
            success = save_geoip_data(
                ip_address=ip,
                device_fingerprint=device_fp,
                client_name=f"Test User {ip}",
                city=city,
                region=region,
                country=country,
                latitude=lat,
                longitude=lon,
                user_agent="Test Agent",
                page_url="/test",
                session_id=str(uuid.uuid4())
            )
            
            if success:
                print(f"    ✅ Added test data for {ip} ({city}, {country})")
            else:
                print(f"    ❌ Failed to add test data for {ip}")
        
        print("  ✅ Test data generation complete")
        return True
        
    except Exception as e:
        print(f"  ❌ Test data generation error: {str(e)}")
        return False

def main():
    """Run comprehensive geolocation diagnostics."""
    print("🔍 AI Analytics Geolocation Diagnostic Tool")
    print("=" * 50)
    
    results = {
        'config': test_environment_config(),
        'maxmind': test_maxmind_service(),
        'database': test_database_structure(),
        'ip_capture': test_ip_capture(),
        'analytics': test_analytics_integration(),
    }
    
    print("\n" + "=" * 50)
    print("📋 DIAGNOSTIC SUMMARY")
    print("=" * 50)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name.upper()}: {status}")
    
    # Overall assessment
    passed_tests = sum(1 for result in results.values() if result)
    total_tests = len(results)
    
    print(f"\n📊 Overall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! Geolocation system is working correctly.")
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
        
        # Offer to generate test data if MaxMind is not working
        if not results['maxmind'] and results['database']:
            print("\n💡 Would you like to generate test data for development? (y/n)")
            response = input().lower().strip()
            if response == 'y':
                generate_test_data()

if __name__ == "__main__":
    main()
