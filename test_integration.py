#!/usr/bin/env python3
"""
Test script to verify comprehensive extraction integration
Tests that the comprehensive extraction is properly integrated into the main pipeline
"""

import os
import sys

# Add the project root to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from app.services.pdf_processor import extract_titles_and_authors_from_ocr_pdf

def test_integration():
    """Test that comprehensive extraction is integrated into the main pipeline"""

    # Path to the problematic PDF
    pdf_path = r"D:\erdb_ai_cursor\test_files\CANOPY\canopy_vol45n1.pdf"

    print("=" * 80)
    print("🧪 TESTING COMPREHENSIVE EXTRACTION INTEGRATION")
    print("=" * 80)
    print(f"PDF Path: {pdf_path}")
    print(f"File exists: {os.path.exists(pdf_path)}")
    print()

    if not os.path.exists(pdf_path):
        print("❌ PDF file not found!")
        return

    # Test the integrated extraction (should use comprehensive method first)
    print("=" * 80)
    print("📄 TESTING INTEGRATED EXTRACTION")
    print("=" * 80)

    try:
        articles = extract_titles_and_authors_from_ocr_pdf(
            pdf_path,
            debug=True,
            skip_first_page=False  # Don't skip first page for testing
        )

        print("\n" + "=" * 80)
        print("📊 INTEGRATED EXTRACTION RESULTS")
        print("=" * 80)
        print(f"Found {len(articles)} articles")

        for i, article in enumerate(articles):
            print(f"\nArticle {i+1}:")
            print(f"  Page: {article.get('page', 'N/A')}")
            print(f"  Title: '{article.get('title', 'N/A')}'")
            print(f"  Authors: <AUTHORS>
            print(f"  Method: {article.get('extraction_method', 'N/A')}")

        # Check if we found the expected title
        found_expected_title = False
        for article in articles:
            title = article.get('title', '').upper()
            if 'COMMUNICATING SCIENCE' in title or '45 YEARS' in title:
                found_expected_title = True
                print(f"\n✅ FOUND EXPECTED TITLE: '{article.get('title', 'N/A')}'")
                break
        
        if not found_expected_title:
            print(f"\n❌ DID NOT FIND EXPECTED TITLE (45 YEARS OF COMMUNICATING SCIENCE FOR A BETTER ENVIRONMENT)")

    except Exception as e:
        print(f"❌ Integrated extraction failed: {str(e)}")
        import traceback
        traceback.print_exc()

    print("\n" + "=" * 80)
    print("✅ INTEGRATION TEST COMPLETE")
    print("=" * 80)

if __name__ == "__main__":
    test_integration() 