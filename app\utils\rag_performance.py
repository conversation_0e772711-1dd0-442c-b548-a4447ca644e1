"""
RAG Pipeline Performance Monitoring
Enhanced metrics for retrieval-augmented generation optimizations
"""

import time
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
from collections import defaultdict
import threading

logger = logging.getLogger(__name__)

@dataclass
class RAGMetric:
    """Metric for RAG pipeline operations"""
    operation_type: str  # 'query', 'retrieval', 'scoring', 'caching'
    category: str
    execution_time: float
    cache_hit: bool = False
    adaptive_k: Optional[int] = None
    base_k: Optional[int] = None
    parallel_workers: Optional[int] = None
    document_count: int = 0
    relevance_scores: List[float] = None
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()
        if self.relevance_scores is None:
            self.relevance_scores = []

class RAGPerformanceMonitor:
    """Enhanced performance monitoring for RAG pipeline"""
    
    def __init__(self):
        self.metrics: List[RAGMetric] = []
        self.cache_stats = {
            'query_hits': 0,
            'query_misses': 0,
            'embedding_hits': 0,
            'embedding_misses': 0
        }
        self.adaptive_retrieval_stats = defaultdict(list)
        self.parallel_processing_stats = []
        self.lock = threading.Lock()
    
    def record_query_metric(self, category: str, execution_time: float, 
                           cache_hit: bool = False, adaptive_k: int = None, 
                           base_k: int = None, document_count: int = 0,
                           relevance_scores: List[float] = None):
        """Record query processing metrics"""
        with self.lock:
            metric = RAGMetric(
                operation_type='query',
                category=category,
                execution_time=execution_time,
                cache_hit=cache_hit,
                adaptive_k=adaptive_k,
                base_k=base_k,
                document_count=document_count,
                relevance_scores=relevance_scores or []
            )
            self.metrics.append(metric)
            
            # Update cache stats
            if cache_hit:
                self.cache_stats['query_hits'] += 1
            else:
                self.cache_stats['query_misses'] += 1
            
            # Update adaptive retrieval stats
            if adaptive_k and base_k:
                self.adaptive_retrieval_stats[category].append({
                    'adaptive_k': adaptive_k,
                    'base_k': base_k,
                    'execution_time': execution_time,
                    'document_count': document_count
                })
    
    def record_parallel_scoring(self, execution_time: float, workers: int, 
                               document_count: int, category: str):
        """Record parallel document scoring metrics"""
        with self.lock:
            metric = RAGMetric(
                operation_type='scoring',
                category=category,
                execution_time=execution_time,
                parallel_workers=workers,
                document_count=document_count
            )
            self.metrics.append(metric)
            
            self.parallel_processing_stats.append({
                'execution_time': execution_time,
                'workers': workers,
                'document_count': document_count,
                'category': category,
                'timestamp': datetime.now().isoformat()
            })
    
    def record_cache_hit(self, cache_type: str):
        """Record cache hit"""
        with self.lock:
            if cache_type == 'query':
                self.cache_stats['query_hits'] += 1
            elif cache_type == 'embedding':
                self.cache_stats['embedding_hits'] += 1
    
    def record_cache_miss(self, cache_type: str):
        """Record cache miss"""
        with self.lock:
            if cache_type == 'query':
                self.cache_stats['query_misses'] += 1
            elif cache_type == 'embedding':
                self.cache_stats['embedding_misses'] += 1
    
    def get_cache_hit_rate(self, cache_type: str = 'query') -> float:
        """Get cache hit rate for specified cache type"""
        with self.lock:
            if cache_type == 'query':
                total = self.cache_stats['query_hits'] + self.cache_stats['query_misses']
                return self.cache_stats['query_hits'] / total if total > 0 else 0
            elif cache_type == 'embedding':
                total = self.cache_stats['embedding_hits'] + self.cache_stats['embedding_misses']
                return self.cache_stats['embedding_hits'] / total if total > 0 else 0
            return 0
    
    def get_adaptive_retrieval_performance(self) -> Dict[str, Any]:
        """Get adaptive retrieval performance statistics"""
        with self.lock:
            stats = {}
            for category, metrics in self.adaptive_retrieval_stats.items():
                if not metrics:
                    continue
                
                avg_time = sum(m['execution_time'] for m in metrics) / len(metrics)
                avg_adaptive_k = sum(m['adaptive_k'] for m in metrics) / len(metrics)
                avg_base_k = sum(m['base_k'] for m in metrics) / len(metrics)
                avg_docs = sum(m['document_count'] for m in metrics) / len(metrics)
                
                stats[category] = {
                    'query_count': len(metrics),
                    'avg_execution_time': avg_time,
                    'avg_adaptive_k': avg_adaptive_k,
                    'avg_base_k': avg_base_k,
                    'avg_document_count': avg_docs,
                    'k_adjustment_ratio': avg_adaptive_k / avg_base_k if avg_base_k > 0 else 1
                }
            
            return stats
    
    def get_parallel_processing_performance(self) -> Dict[str, Any]:
        """Get parallel processing performance statistics"""
        with self.lock:
            if not self.parallel_processing_stats:
                return {}
            
            total_time = sum(s['execution_time'] for s in self.parallel_processing_stats)
            avg_time = total_time / len(self.parallel_processing_stats)
            avg_workers = sum(s['workers'] for s in self.parallel_processing_stats) / len(self.parallel_processing_stats)
            avg_docs = sum(s['document_count'] for s in self.parallel_processing_stats) / len(self.parallel_processing_stats)
            
            return {
                'total_operations': len(self.parallel_processing_stats),
                'avg_execution_time': avg_time,
                'avg_workers': avg_workers,
                'avg_document_count': avg_docs,
                'total_time_saved': total_time  # Estimate based on parallel vs sequential
            }
    
    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """Get comprehensive RAG performance statistics"""
        with self.lock:
            return {
                'cache_performance': {
                    'query_hit_rate': self.get_cache_hit_rate('query'),
                    'embedding_hit_rate': self.get_cache_hit_rate('embedding'),
                    'total_query_hits': self.cache_stats['query_hits'],
                    'total_query_misses': self.cache_stats['query_misses'],
                    'total_embedding_hits': self.cache_stats['embedding_hits'],
                    'total_embedding_misses': self.cache_stats['embedding_misses']
                },
                'adaptive_retrieval': self.get_adaptive_retrieval_performance(),
                'parallel_processing': self.get_parallel_processing_performance(),
                'total_metrics': len(self.metrics),
                'monitoring_start': self.metrics[0].timestamp if self.metrics else None,
                'last_update': self.metrics[-1].timestamp if self.metrics else None
            }
    
    def reset_stats(self):
        """Reset all performance statistics"""
        with self.lock:
            self.metrics.clear()
            self.cache_stats = {
                'query_hits': 0,
                'query_misses': 0,
                'embedding_hits': 0,
                'embedding_misses': 0
            }
            self.adaptive_retrieval_stats.clear()
            self.parallel_processing_stats.clear()
            logger.info("RAG performance statistics reset")

# Global RAG performance monitor
_rag_monitor: Optional[RAGPerformanceMonitor] = None

def get_rag_monitor() -> RAGPerformanceMonitor:
    """Get the global RAG performance monitor instance"""
    global _rag_monitor
    if _rag_monitor is None:
        _rag_monitor = RAGPerformanceMonitor()
    return _rag_monitor

def monitor_rag_operation(operation_type: str):
    """Decorator for monitoring RAG operations"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            monitor = get_rag_monitor()
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                # Extract relevant information from args/kwargs for monitoring
                category = kwargs.get('category') or (args[0] if args else 'unknown')
                
                if operation_type == 'query':
                    # Record query metrics
                    cache_hit = getattr(result, 'metadata', {}).get('cached', False) if isinstance(result, dict) else False
                    document_count = getattr(result, 'metadata', {}).get('document_count', 0) if isinstance(result, dict) else 0
                    
                    monitor.record_query_metric(
                        category=str(category),
                        execution_time=execution_time,
                        cache_hit=cache_hit,
                        document_count=document_count
                    )
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"RAG operation {operation_type} failed after {execution_time:.3f}s: {e}")
                raise
        
        return wrapper
    return decorator
