#!/usr/bin/env python3
"""
Test script to verify the response greeting system is working properly.
This script tests the response-type greetings specifically.
"""

import sys
import os
import requests
import json

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_response_greeting_service():
    """Test the GreetingManager service for response-type greetings."""
    print("Testing GreetingManager service for response greetings...")
    
    try:
        from app.services.greeting_service import GreetingManager
        
        # Initialize greeting manager
        greeting_manager = GreetingManager()
        
        # Test getting a response-type greeting
        context = {
            'device_fingerprint': 'test-device-123',
            'time_of_day': 'afternoon',
            'is_new_session': False,
            'greeting_type': 'response'  # Explicitly request response type
        }
        
        greeting_data = greeting_manager.get_contextual_greeting('TestUser', context)
        
        print(f"✓ Response greeting service working!")
        print(f"  Greeting: {greeting_data['greeting']}")
        print(f"  Source: {greeting_data.get('source', 'unknown')}")
        print(f"  Template Type: {greeting_data.get('template_type', 'unknown')}")
        
        # Test multiple calls to see variation
        print("\n  Testing greeting variation (5 samples):")
        for i in range(5):
            greeting_data = greeting_manager.get_contextual_greeting(f'User{i+1}', context)
            print(f"    {i+1}. {greeting_data['greeting']}")
        
        return True
        
    except Exception as e:
        print(f"✗ Response greeting service failed: {str(e)}")
        return False

def test_response_greeting_api():
    """Test the greeting API endpoint for response-type greetings."""
    print("\nTesting greeting API endpoint for response greetings...")
    
    try:
        # Test data for response greeting
        test_data = {
            'client_name': 'TestUser',
            'context': {
                'device_fingerprint': 'test-device-123',
                'time_of_day': 'afternoon',
                'is_new_session': False,
                'greeting_type': 'response'  # Explicitly request response type
            }
        }
        
        # Make request to the API
        response = requests.post(
            'http://localhost:5000/api/greeting',
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✓ Response greeting API working!")
                print(f"  Greeting: {result['greeting']}")
                print(f"  Template Type: {result.get('greeting_data', {}).get('template_type', 'unknown')}")
                
                # Test multiple calls to see variation
                print("\n  Testing API greeting variation (3 samples):")
                for i in range(3):
                    test_data['client_name'] = f'User{i+1}'
                    resp = requests.post('http://localhost:5000/api/greeting', json=test_data, headers={'Content-Type': 'application/json'}, timeout=5)
                    if resp.status_code == 200:
                        res = resp.json()
                        if res.get('success'):
                            print(f"    {i+1}. {res['greeting']}")
                
                return True
            else:
                print(f"✗ API returned error: {result.get('error', 'unknown error')}")
                return False
        else:
            print(f"✗ API request failed with status {response.status_code}")
            print(f"  Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("✗ Could not connect to the application. Make sure it's running on localhost:5000")
        return False
    except Exception as e:
        print(f"✗ API test failed: {str(e)}")
        return False

def test_response_templates_in_db():
    """Test if response-type greeting templates exist in the database."""
    print("\nTesting response-type greeting templates in database...")
    
    try:
        from app.services.greeting_service import GreetingManager
        
        greeting_manager = GreetingManager()
        templates = greeting_manager.get_greeting_templates('response')
        
        if templates:
            print(f"✓ Found {len(templates)} response-type greeting templates in database")
            
            # Show examples
            for i, template in enumerate(templates[:5]):
                print(f"  Template {i+1}: {template.get('greeting_text', 'no text')}")
            
            return True
        else:
            print("✗ No response-type greeting templates found in database")
            return False
            
    except Exception as e:
        print(f"✗ Database test failed: {str(e)}")
        return False

def main():
    """Run all response greeting tests."""
    print("=== Response Greeting System Test ===\n")
    
    # Test the service layer for response greetings
    service_ok = test_response_greeting_service()
    
    # Test database response templates
    db_ok = test_response_templates_in_db()
    
    # Test the API endpoint for response greetings
    api_ok = test_response_greeting_api()
    
    print("\n=== Test Results ===")
    print(f"Response Greeting Service: {'✓ PASS' if service_ok else '✗ FAIL'}")
    print(f"Response Templates in DB: {'✓ PASS' if db_ok else '✗ FAIL'}")
    print(f"Response Greeting API: {'✓ PASS' if api_ok else '✗ FAIL'}")
    
    if service_ok and db_ok:
        print("\n✓ Response greeting system is working correctly!")
        if not api_ok:
            print("⚠ API test failed - make sure the Flask app is running")
        print("\n📝 Next steps:")
        print("1. Start the Flask application")
        print("2. Open the main page and enter your name")
        print("3. Ask a question and check if the AI response starts with a personalized greeting")
        print("4. Try multiple questions to see greeting variation")
    else:
        print("\n✗ Response greeting system has issues that need to be fixed")
    
    return service_ok and db_ok

if __name__ == '__main__':
    main()
