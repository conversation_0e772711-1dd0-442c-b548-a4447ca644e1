"""
Enhanced Chunking Service using LlamaIndex with Ollama Embeddings
Provides semantic-aware and adaptive text chunking capabilities.
"""

import os
import logging
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

# Set up logger early
logger = logging.getLogger(__name__)

# LangChain imports for compatibility
from langchain.schema import Document

# LlamaIndex imports with correct paths
try:
    # Correct import paths for LlamaIndex 0.13.0+
    from llama_index.core.text_splitter import SentenceSplitter
    from llama_index.core.node_parser.text.semantic_splitter import SemanticSplitterNodeParser
    from llama_index.embeddings.ollama import OllamaEmbedding
    LLAMAINDEX_AVAILABLE = True
    logger.info("✅ LlamaIndex successfully imported")
except ImportError as e:
    logger.warning(f"LlamaIndex import failed: {e}")
    try:
        # Try alternative import paths for older versions
        from llama_index.text_splitter import SentenceSplitter
        from llama_index.embeddings.ollama import OllamaEmbedding
        # For older versions, SemanticSplitter might not be available
        SemanticSplitterNodeParser = None
        LLAMAINDEX_AVAILABLE = True
        logger.info("✅ LlamaIndex imported with legacy paths")
    except ImportError as e2:
        logger.warning(f"LlamaIndex legacy import also failed: {e2}")
        # Fallback to LangChain
        from langchain.text_splitter import RecursiveCharacterTextSplitter
        LLAMAINDEX_AVAILABLE = False
        SemanticSplitterNodeParser = None

from config.chunking_config import get_chunking_config
from app.services.content_type_detector import ContentTypeDetector
from app.utils.performance_monitor import performance_monitor

class ChunkingError(Exception):
    """Custom exception for chunking operations"""
    pass

class EnhancedChunkingService:
    """Enhanced chunking service with semantic awareness and adaptive strategies"""
    
    def __init__(self, config=None):
        self.config = config or get_chunking_config()
        self.content_detector = ContentTypeDetector()
        self._embed_model = None
        self._fallback_splitter = None
        
        # Initialize fallback splitter
        self._init_fallback_splitter()
        
        # Try to initialize LlamaIndex components
        if LLAMAINDEX_AVAILABLE:
            self._init_llamaindex_components()
        else:
            logger.warning("LlamaIndex not available. Using LangChain fallback.")
    
    def _init_fallback_splitter(self):
        """Initialize fallback RecursiveCharacterTextSplitter"""
        try:
            from langchain.text_splitter import RecursiveCharacterTextSplitter
            self._fallback_splitter = RecursiveCharacterTextSplitter(
                chunk_size=self.config.default_chunk_size,
                chunk_overlap=self.config.default_chunk_overlap
            )
            logger.info("Initialized fallback text splitter")
        except Exception as e:
            logger.error(f"Failed to initialize fallback splitter: {e}")
            raise ChunkingError(f"Cannot initialize any text splitter: {e}")
    
    def _init_llamaindex_components(self):
        """Initialize LlamaIndex embedding model"""
        try:
            # Try LlamaIndex Ollama embedding first
            self._embed_model = OllamaEmbedding(
                model_name=self.config.embedding_model,
                base_url=self.config.ollama_base_url,
                request_timeout=60.0
            )
            logger.info(f"Initialized LlamaIndex Ollama embedding: {self.config.embedding_model}")
        except Exception as e:
            logger.warning(f"Failed to initialize LlamaIndex Ollama embedding: {e}")
            # Try to create a compatibility wrapper using existing Ollama setup
            try:
                self._embed_model = self._create_ollama_compatibility_wrapper()
                logger.info("Created Ollama compatibility wrapper for semantic chunking")
            except Exception as wrapper_error:
                logger.warning(f"Failed to create compatibility wrapper: {wrapper_error}")
                self._embed_model = None

    def _create_ollama_compatibility_wrapper(self):
        """Create a compatibility wrapper for existing Ollama embeddings"""
        try:
            from langchain_ollama.embeddings import OllamaEmbeddings

            # Create LangChain Ollama embeddings
            langchain_embeddings = OllamaEmbeddings(
                model=self.config.embedding_model,
                base_url=self.config.ollama_base_url
            )

            # Create a simple wrapper that provides the interface expected by LlamaIndex
            class OllamaCompatibilityWrapper:
                def __init__(self, langchain_embeddings):
                    self.langchain_embeddings = langchain_embeddings

                def get_text_embedding(self, text):
                    """Get embedding for a single text"""
                    return self.langchain_embeddings.embed_query(text)

                def get_text_embeddings(self, texts):
                    """Get embeddings for multiple texts"""
                    return self.langchain_embeddings.embed_documents(texts)

            return OllamaCompatibilityWrapper(langchain_embeddings)

        except Exception as e:
            logger.error(f"Failed to create Ollama compatibility wrapper: {e}")
            return None
    
    def get_semantic_splitter(self, buffer_size=1, breakpoint_percentile_threshold=95):
        """Get semantic splitter with Ollama embeddings"""
        if not LLAMAINDEX_AVAILABLE or self._embed_model is None or SemanticSplitterNodeParser is None:
            logger.warning("Semantic splitting not available, using fallback")
            return self._fallback_splitter

        try:
            # Use the correct class name for LlamaIndex 0.13.0+
            semantic_splitter = SemanticSplitterNodeParser(
                buffer_size=buffer_size,
                embed_model=self._embed_model,
                breakpoint_percentile_threshold=breakpoint_percentile_threshold
            )
            logger.info(f"Created semantic splitter with buffer_size={buffer_size}, threshold={breakpoint_percentile_threshold}")
            return semantic_splitter
        except Exception as e:
            logger.error(f"Failed to create semantic splitter: {e}")
            return self._fallback_splitter

    def get_optimal_chunking_strategy(self, content_type: str = "general", content_length: int = 0):
        """Determine optimal chunking strategy based on content characteristics"""
        # Use semantic chunking for longer, technical content
        if content_length > 5000 and content_type in ["technical", "research", "manual"]:
            if LLAMAINDEX_AVAILABLE and self._embed_model is not None:
                logger.info(f"Using semantic chunking for {content_type} content ({content_length} chars)")
                return "semantic"

        # Use sentence-based chunking for medium content
        elif content_length > 1000:
            logger.info(f"Using sentence-based chunking for {content_type} content ({content_length} chars)")
            return "sentence"

        # Use fixed-size chunking for shorter content
        else:
            logger.info(f"Using fixed-size chunking for {content_type} content ({content_length} chars)")
            return "fixed"

    def chunk_with_optimal_strategy(self, text: str, content_type: str = "general",
                                   chunk_size: int = None, chunk_overlap: int = None):
        """Chunk text using the optimal strategy based on content characteristics"""
        content_length = len(text)
        strategy = self.get_optimal_chunking_strategy(content_type, content_length)

        # Use provided parameters or defaults from config
        chunk_size = chunk_size or self.config.default_chunk_size
        chunk_overlap = chunk_overlap or self.config.default_chunk_overlap

        if strategy == "semantic" and LLAMAINDEX_AVAILABLE:
            try:
                # Use semantic chunking
                semantic_splitter = self.get_semantic_splitter(
                    buffer_size=self.config.semantic_buffer_size,
                    breakpoint_percentile_threshold=self.config.semantic_breakpoint_threshold
                )

                # Convert text to LlamaIndex Document
                from llama_index.core.schema import Document as LlamaDocument
                doc = LlamaDocument(text=text)

                # Split using semantic splitter
                nodes = semantic_splitter.get_nodes_from_documents([doc])

                # Convert back to LangChain documents
                from langchain.schema import Document
                chunks = []
                for i, node in enumerate(nodes):
                    chunk = Document(
                        page_content=node.text,
                        metadata={
                            "chunk_index": i,
                            "chunking_strategy": "semantic",
                            "content_type": content_type,
                            "source_length": content_length
                        }
                    )
                    chunks.append(chunk)

                logger.info(f"Semantic chunking produced {len(chunks)} chunks from {content_length} characters")
                return chunks

            except Exception as e:
                logger.error(f"Semantic chunking failed: {e}, falling back to sentence chunking")
                strategy = "sentence"

        if strategy == "sentence":
            try:
                # Use sentence-based chunking
                sentence_splitter = self.get_sentence_splitter(chunk_size, chunk_overlap)
                chunks = sentence_splitter.split_text(text)

                # Convert to Document objects
                from langchain.schema import Document
                documents = []
                for i, chunk in enumerate(chunks):
                    doc = Document(
                        page_content=chunk,
                        metadata={
                            "chunk_index": i,
                            "chunking_strategy": "sentence",
                            "content_type": content_type,
                            "source_length": content_length
                        }
                    )
                    documents.append(doc)

                logger.info(f"Sentence chunking produced {len(documents)} chunks from {content_length} characters")
                return documents

            except Exception as e:
                logger.error(f"Sentence chunking failed: {e}, falling back to fixed-size chunking")
                strategy = "fixed"

        # Fixed-size chunking (fallback)
        try:
            splitter = self.get_text_splitter(chunk_size, chunk_overlap)
            chunks = splitter.split_text(text)

            # Convert to Document objects
            from langchain.schema import Document
            documents = []
            for i, chunk in enumerate(chunks):
                doc = Document(
                    page_content=chunk,
                    metadata={
                        "chunk_index": i,
                        "chunking_strategy": "fixed",
                        "content_type": content_type,
                        "source_length": content_length
                    }
                )
                documents.append(doc)

            logger.info(f"Fixed-size chunking produced {len(documents)} chunks from {content_length} characters")
            return documents

        except Exception as e:
            logger.error(f"All chunking strategies failed: {e}")
            raise
    
    def get_sentence_splitter(self, chunk_size=800, chunk_overlap=200, 
                            paragraph_separator="\n\n", secondary_chunking_regex=None):
        """Get sentence-aware splitter"""
        if not LLAMAINDEX_AVAILABLE:
            logger.warning("LlamaIndex sentence splitter not available, using fallback")
            return self._fallback_splitter
        
        try:
            kwargs = {
                'chunk_size': chunk_size,
                'chunk_overlap': chunk_overlap,
                'paragraph_separator': paragraph_separator
            }
            
            if secondary_chunking_regex:
                kwargs['secondary_chunking_regex'] = secondary_chunking_regex
            
            return SentenceSplitter(**kwargs)
        except Exception as e:
            logger.error(f"Failed to create sentence splitter: {e}")
            return self._fallback_splitter
    
    @performance_monitor(track_memory=True, track_cpu=True)
    def adaptive_chunk(self, documents: List[Document], content_type: str = None) -> List[Document]:
        """
        Apply adaptive chunking based on content type
        
        Args:
            documents: List of LangChain Document objects
            content_type: Optional content type override
            
        Returns:
            List of chunked Document objects
        """
        if not documents:
            return []
        
        try:
            # Detect content type if not provided
            if content_type is None:
                sample_text = self._get_sample_text(documents)
                content_type = self.content_detector.detect_content_type(sample_text)
            
            logger.info(f"Using adaptive chunking for content type: {content_type}")
            
            # Get configuration for content type
            type_config = self.config.get_config_for_content_type(content_type)
            
            # Select and apply chunking strategy
            if type_config.get('use_semantic', False) and LLAMAINDEX_AVAILABLE and self._embed_model:
                return self._semantic_chunk(documents, type_config)
            elif type_config.get('strategy') == 'sentence_aware':
                return self._sentence_aware_chunk(documents, type_config)
            else:
                return self._fallback_chunk(documents, type_config)
                
        except Exception as e:
            logger.error(f"Adaptive chunking failed: {e}. Using fallback.")
            return self._fallback_chunk(documents)
    
    def _semantic_chunk(self, documents: List[Document], config: Dict[str, Any]) -> List[Document]:
        """Apply semantic chunking using LlamaIndex"""
        try:
            logger.info("Applying semantic chunking with LlamaIndex")

            splitter = self.get_semantic_splitter(
                buffer_size=config.get('buffer_size', 1),
                breakpoint_percentile_threshold=config.get('breakpoint_percentile_threshold', 95)
            )

            # Check if we got a fallback splitter
            if splitter == self._fallback_splitter:
                logger.warning("Using fallback splitter for semantic chunking")
                return self._fallback_chunk(documents, config)

            # Convert LangChain Documents to LlamaIndex Documents for processing
            try:
                from llama_index.core.schema import Document as LlamaDocument

                chunks = []
                for doc in documents:
                    try:
                        # Create LlamaIndex document
                        llama_doc = LlamaDocument(text=doc.page_content, metadata=doc.metadata)

                        # Apply node parser (semantic splitter)
                        nodes = splitter.get_nodes_from_documents([llama_doc])

                        # Convert back to LangChain Documents
                        for node in nodes:
                            new_doc = Document(
                                page_content=node.text,
                                metadata=doc.metadata.copy()
                            )
                            # Add node-specific metadata if available
                            if hasattr(node, 'metadata') and node.metadata:
                                new_doc.metadata.update(node.metadata)
                            chunks.append(new_doc)

                    except Exception as doc_error:
                        logger.warning(f"Failed to process document with semantic splitter: {doc_error}")
                        # Fallback to sentence splitting for this document
                        fallback_chunks = self._sentence_aware_chunk([doc], config)
                        chunks.extend(fallback_chunks)

                logger.info(f"Semantic chunking created {len(chunks)} chunks from {len(documents)} documents")
                return chunks

            except ImportError:
                logger.warning("LlamaIndex Document import failed, using text-based approach")
                # Fallback to text-based processing
                chunks = []
                for doc in documents:
                    if hasattr(splitter, 'split_text'):
                        text_chunks = splitter.split_text(doc.page_content)
                        for chunk in text_chunks:
                            new_doc = Document(
                                page_content=chunk,
                                metadata=doc.metadata.copy()
                            )
                            chunks.append(new_doc)
                    else:
                        # Use fallback splitter
                        fallback_chunks = self._fallback_splitter.split_documents([doc])
                        chunks.extend(fallback_chunks)

                logger.info(f"Text-based semantic chunking created {len(chunks)} chunks from {len(documents)} documents")
                return chunks

        except Exception as e:
            logger.error(f"Semantic chunking failed: {e}")
            return self._fallback_chunk(documents, config)
    
    def _sentence_aware_chunk(self, documents: List[Document], config: Dict[str, Any]) -> List[Document]:
        """Apply sentence-aware chunking"""
        try:
            splitter = self.get_sentence_splitter(
                chunk_size=config.get('chunk_size', 800),
                chunk_overlap=config.get('chunk_overlap', 200),
                paragraph_separator=config.get('paragraph_separator', '\n\n'),
                secondary_chunking_regex=config.get('secondary_chunking_regex')
            )
            
            if hasattr(splitter, 'split_text') and LLAMAINDEX_AVAILABLE:
                # LlamaIndex interface
                chunks = []
                for i, doc in enumerate(documents):
                    text_chunks = splitter.split_text(doc.page_content)
                    for chunk in text_chunks:
                        new_doc = Document(
                            page_content=chunk,
                            metadata=doc.metadata.copy()
                        )
                        chunks.append(new_doc)
            else:
                # LangChain interface
                chunks = splitter.split_documents(documents)
            
            logger.info(f"Sentence-aware chunking created {len(chunks)} chunks from {len(documents)} documents")
            return chunks
            
        except Exception as e:
            logger.error(f"Sentence-aware chunking failed: {e}")
            return self._fallback_chunk(documents, config)
    
    def _fallback_chunk(self, documents: List[Document], config: Dict[str, Any] = None) -> List[Document]:
        """Apply fallback chunking using RecursiveCharacterTextSplitter"""
        try:
            if config:
                # Create splitter with config parameters
                from langchain.text_splitter import RecursiveCharacterTextSplitter
                splitter = RecursiveCharacterTextSplitter(
                    chunk_size=config.get('chunk_size', self.config.default_chunk_size),
                    chunk_overlap=config.get('chunk_overlap', self.config.default_chunk_overlap)
                )
            else:
                splitter = self._fallback_splitter
            
            chunks = splitter.split_documents(documents)
            logger.info(f"Fallback chunking created {len(chunks)} chunks from {len(documents)} documents")
            return chunks
            
        except Exception as e:
            logger.error(f"Fallback chunking failed: {e}")
            raise ChunkingError(f"All chunking strategies failed: {e}")
    
    def _get_sample_text(self, documents: List[Document], max_sample_length: int = 2000) -> str:
        """Extract sample text for content type detection"""
        sample_texts = []
        current_length = 0
        
        for doc in documents:
            if current_length >= max_sample_length:
                break
            
            text = doc.page_content[:max_sample_length - current_length]
            sample_texts.append(text)
            current_length += len(text)
        
        return " ".join(sample_texts)
    
    @performance_monitor(track_memory=True, track_cpu=True)
    def parallel_chunk_processing(self, documents: List[Document], content_type: str = None) -> List[Document]:
        """Process documents in parallel with performance monitoring"""
        if not self.config.enable_parallel_processing or len(documents) < 10:
            return self.adaptive_chunk(documents, content_type)
        
        # Calculate optimal batch size
        batch_size = self._calculate_optimal_batch_size(documents)
        batches = [documents[i:i + batch_size] for i in range(0, len(documents), batch_size)]
        
        results = []
        with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
            future_to_batch = {
                executor.submit(self.adaptive_chunk, batch, content_type): batch 
                for batch in batches
            }
            
            for future in as_completed(future_to_batch):
                try:
                    batch_results = future.result()
                    results.extend(batch_results)
                except Exception as exc:
                    logger.error(f'Batch processing failed: {exc}')
                    
        logger.info(f"Parallel processing created {len(results)} chunks from {len(documents)} documents")
        return results
    
    def _calculate_optimal_batch_size(self, documents: List[Document]) -> int:
        """Calculate optimal batch size based on document size and system resources"""
        total_size = sum(len(doc.page_content) for doc in documents)
        avg_doc_size = total_size / len(documents) if documents else 0
        
        # Adaptive batch sizing
        if avg_doc_size > 10000:  # Large documents
            return max(2, len(documents) // (self.config.max_workers * 2))
        elif avg_doc_size > 1000:  # Medium documents
            return max(5, len(documents) // self.config.max_workers)
        else:  # Small documents
            return max(10, len(documents) // self.config.max_workers)
