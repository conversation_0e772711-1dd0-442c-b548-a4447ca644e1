<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Response Greeting Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        #greeting-samples {
            min-height: 150px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            background-color: #f9f9f9;
            white-space: pre-line;
        }
        .sample-greeting {
            background-color: #e3f2fd;
            padding: 8px;
            margin: 5px 0;
            border-radius: 4px;
            border-left: 4px solid #2196f3;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <h1>Chat Response Greeting System Test</h1>
    
    <div class="test-container">
        <h2>🎯 Test Overview</h2>
        <div class="info">
            <p><strong>Purpose:</strong> Test the integration of dynamic greeting templates with AI chat responses.</p>
            <p><strong>Expected Behavior:</strong> When users ask questions, AI responses should start with personalized greetings like "Great question, John." or "Here's what I can tell you, Sarah:" instead of generic patterns.</p>
        </div>
    </div>

    <div class="test-section">
        <h3>1. Test Response Greeting API</h3>
        <p>This tests the greeting API endpoint specifically for response-type greetings.</p>
        
        <div>
            <label for="test-name">User Name:</label>
            <input type="text" id="test-name" value="TestUser" placeholder="Enter test name">
            <button onclick="testResponseGreetingAPI()" id="test-greeting-btn">Test Response Greeting</button>
            <button onclick="generateMultipleGreetings()" id="test-multiple-btn">Generate 5 Samples</button>
        </div>
        
        <div id="greeting-samples">
            <em>Click the buttons above to test response greetings...</em>
        </div>
    </div>

    <div class="test-section">
        <h3>2. Integration Test Instructions</h3>
        <div class="warning">
            <strong>⚠️ Manual Testing Required</strong>
            <p>To test the complete chat response greeting integration:</p>
            <ol>
                <li><strong>Start the Flask application:</strong> <code>python app/__main__.py</code></li>
                <li><strong>Open the main application:</strong> <a href="http://localhost:5000" target="_blank">http://localhost:5000</a></li>
                <li><strong>Enter your name</strong> in the welcome modal</li>
                <li><strong>Select a category</strong> from the dropdown</li>
                <li><strong>Ask a question</strong> and submit it</li>
                <li><strong>Check the AI response</strong> - it should start with a personalized greeting</li>
                <li><strong>Ask multiple questions</strong> to see greeting variation</li>
            </ol>
        </div>
    </div>

    <div class="test-section">
        <h3>3. Expected Response Format</h3>
        <div class="info">
            <p><strong>Before the fix:</strong></p>
            <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0;">
                <em>AI response without greeting:</em><br>
                "Based on the available information, marine biodiversity refers to..."
            </div>
            
            <p><strong>After the fix:</strong></p>
            <div style="background: #e8f5e8; padding: 10px; border-radius: 4px; margin: 10px 0;">
                <em>AI response with personalized greeting:</em><br>
                "<strong>Great question, John.</strong> Based on the available information, marine biodiversity refers to..."
            </div>
        </div>
    </div>

    <div class="test-section">
        <h3>4. Troubleshooting</h3>
        <div class="warning">
            <p><strong>If greetings are not appearing in chat responses:</strong></p>
            <ul>
                <li>Check browser console for JavaScript errors</li>
                <li>Verify the Flask application is running</li>
                <li>Test the greeting API directly using the test above</li>
                <li>Check that the user name was entered correctly</li>
                <li>Ensure the greeting templates exist in the database</li>
            </ul>
        </div>
    </div>

    <script>
        async function testResponseGreetingAPI() {
            const resultDiv = document.getElementById('greeting-samples');
            const testName = document.getElementById('test-name').value.trim() || 'TestUser';
            const testBtn = document.getElementById('test-greeting-btn');
            
            testBtn.disabled = true;
            resultDiv.innerHTML = '<div class="info">Testing response greeting API...</div>';
            
            try {
                const response = await fetch('/api/greeting', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        client_name: testName,
                        context: {
                            device_fingerprint: 'test-device-' + Date.now(),
                            time_of_day: getTimeOfDay(),
                            is_new_session: false,
                            greeting_type: 'response'  // Explicitly request response type
                        }
                    })
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <strong>✓ Response Greeting API Test Successful!</strong>
                        </div>
                        <div class="sample-greeting">
                            <strong>Sample Greeting:</strong> ${result.greeting}
                        </div>
                        <div style="margin-top: 10px; font-size: 0.9em; color: #666;">
                            <strong>Template Type:</strong> ${result.greeting_data?.template_type || 'unknown'}<br>
                            <strong>Source:</strong> ${result.greeting_data?.source || 'unknown'}<br>
                            <strong>Template ID:</strong> ${result.greeting_data?.template_id || 'unknown'}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <strong>✗ API Error:</strong> ${result.error || 'Unknown error'}<br>
                            <strong>Fallback Greeting:</strong> ${result.greeting || 'None'}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <strong>✗ Network Error:</strong> ${error.message}<br>
                        <em>Make sure the Flask application is running on localhost:5000</em>
                    </div>
                `;
            } finally {
                testBtn.disabled = false;
            }
        }
        
        async function generateMultipleGreetings() {
            const resultDiv = document.getElementById('greeting-samples');
            const testName = document.getElementById('test-name').value.trim() || 'TestUser';
            const testBtn = document.getElementById('test-multiple-btn');
            
            testBtn.disabled = true;
            resultDiv.innerHTML = '<div class="info">Generating multiple greeting samples...</div>';
            
            try {
                const greetings = [];
                
                for (let i = 0; i < 5; i++) {
                    const response = await fetch('/api/greeting', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            client_name: testName,
                            context: {
                                device_fingerprint: 'test-device-' + Date.now() + '-' + i,
                                time_of_day: getTimeOfDay(),
                                is_new_session: false,
                                greeting_type: 'response'
                            }
                        })
                    });
                    
                    const result = await response.json();
                    if (response.ok && result.success) {
                        greetings.push(result.greeting);
                    }
                    
                    // Small delay to avoid overwhelming the server
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
                
                if (greetings.length > 0) {
                    let html = '<div class="success"><strong>✓ Generated ' + greetings.length + ' greeting samples:</strong></div>';
                    greetings.forEach((greeting, index) => {
                        html += `<div class="sample-greeting">${index + 1}. ${greeting}</div>`;
                    });
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = '<div class="error">✗ Failed to generate greeting samples</div>';
                }
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <strong>✗ Error generating samples:</strong> ${error.message}
                    </div>
                `;
            } finally {
                testBtn.disabled = false;
            }
        }
        
        function getTimeOfDay() {
            const hour = new Date().getHours();
            if (hour < 12) {
                return 'morning';
            } else if (hour < 18) {
                return 'afternoon';
            } else {
                return 'evening';
            }
        }
        
        // Auto-test on page load if running from the Flask server
        if (window.location.hostname === 'localhost' && window.location.port === '5000') {
            window.addEventListener('load', () => {
                setTimeout(testResponseGreetingAPI, 1000);
            });
        }
    </script>
</body>
</html>
