"""
Backup Management Routes for Admin Dashboard

This module provides routes for managing system backups through the admin interface.
"""

import os
import sys
import logging
import datetime
from pathlib import Path
from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.routes.auth import admin_required, function_permission_required
from scripts.backup.scheduled_backup import list_backups, cleanup_old_backups
from scripts.backup.create_backup import create_backup

# Configure logging
logger = logging.getLogger(__name__)

# Create blueprint
backup_bp = Blueprint('backup', __name__, url_prefix='/admin/backup')

@backup_bp.route('/')
@admin_required
@function_permission_required('backup_management')
def backup_dashboard():
    """Backup management dashboard"""
    try:
        # Get backup configuration
        backup_dir = os.getenv('BACKUP_DIR', './backups')
        retention_days = int(os.getenv('BACKUP_RETENTION_DAYS', '30'))
        
        # List available backups
        backups = list_backups(backup_dir)
        
        # Calculate backup statistics
        total_size = sum(backup['size'] for backup in backups)
        total_count = len(backups)
        
        # Get disk usage
        backup_path = Path(backup_dir)
        disk_usage = 0
        if backup_path.exists():
            disk_usage = sum(f.stat().st_size for f in backup_path.rglob('*') if f.is_file())
        
        return render_template('backup_dashboard.html',
                             backups=backups,
                             total_size=total_size,
                             total_count=total_count,
                             disk_usage=disk_usage,
                             retention_days=retention_days,
                             backup_dir=backup_dir)
    
    except Exception as e:
        logger.error(f"Error loading backup dashboard: {str(e)}")
        flash(f"Error loading backup dashboard: {str(e)}", "error")
        return redirect(url_for('admin.admin_dashboard'))

@backup_bp.route('/create', methods=['POST'])
@admin_required
@function_permission_required('backup_management')
def create_backup_route():
    """Create a new backup"""
    try:
        logger.info("Creating backup via admin interface")
        
        success = create_backup()
        
        if success:
            flash("Backup created successfully", "success")
            logger.info("Backup created successfully via admin interface")
        else:
            flash("Failed to create backup", "error")
            logger.error("Backup creation failed via admin interface")
        
        return redirect(url_for('backup.backup_dashboard'))
    
    except Exception as e:
        logger.error(f"Error creating backup: {str(e)}")
        flash(f"Error creating backup: {str(e)}", "error")
        return redirect(url_for('backup.backup_dashboard'))

@backup_bp.route('/cleanup', methods=['POST'])
@admin_required
@function_permission_required('backup_management')
def cleanup_backups_route():
    """Clean up old backups"""
    try:
        retention_days = int(request.form.get('retention_days', 30))
        backup_dir = os.getenv('BACKUP_DIR', './backups')
        
        logger.info(f"Cleaning up backups older than {retention_days} days")
        
        deleted_count = cleanup_old_backups(backup_dir, retention_days)
        
        if deleted_count > 0:
            flash(f"Cleaned up {deleted_count} old backups", "success")
            logger.info(f"Cleaned up {deleted_count} old backups")
        else:
            flash("No old backups to clean up", "info")
            logger.info("No old backups to clean up")
        
        return redirect(url_for('backup.backup_dashboard'))
    
    except Exception as e:
        logger.error(f"Error cleaning up backups: {str(e)}")
        flash(f"Error cleaning up backups: {str(e)}", "error")
        return redirect(url_for('backup.backup_dashboard'))

@backup_bp.route('/api/list')
@admin_required
@function_permission_required('backup_management')
def api_list_backups():
    """API endpoint to list backups"""
    try:
        backup_dir = os.getenv('BACKUP_DIR', './backups')
        backups = list_backups(backup_dir)
        
        # Format backup data for API response
        backup_data = []
        for backup in backups:
            backup_data.append({
                'filename': backup['filename'],
                'path': backup['path'],
                'size': backup['size'],
                'size_formatted': f"{backup['size'] / (1024*1024):.2f} MB",
                'date': backup['date'],
                'age_days': backup['age_days']
            })
        
        return jsonify({
            'success': True,
            'backups': backup_data,
            'total_count': len(backup_data)
        })
    
    except Exception as e:
        logger.error(f"Error listing backups via API: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'backups': []
        }), 500

@backup_bp.route('/api/create', methods=['POST'])
@admin_required
@function_permission_required('backup_management')
def api_create_backup():
    """API endpoint to create backup"""
    try:
        logger.info("Creating backup via API")
        
        success = create_backup()
        
        if success:
            # Get updated backup list
            backup_dir = os.getenv('BACKUP_DIR', './backups')
            backups = list_backups(backup_dir)
            
            return jsonify({
                'success': True,
                'message': 'Backup created successfully',
                'backup_count': len(backups)
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to create backup'
            }), 500
    
    except Exception as e:
        logger.error(f"Error creating backup via API: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@backup_bp.route('/api/cleanup', methods=['POST'])
@admin_required
@function_permission_required('backup_management')
def api_cleanup_backups():
    """API endpoint to clean up old backups"""
    try:
        data = request.get_json()
        retention_days = data.get('retention_days', 30) if data else 30
        backup_dir = os.getenv('BACKUP_DIR', './backups')
        
        logger.info(f"Cleaning up backups older than {retention_days} days via API")
        
        deleted_count = cleanup_old_backups(backup_dir, retention_days)
        
        return jsonify({
            'success': True,
            'message': f'Cleaned up {deleted_count} old backups',
            'deleted_count': deleted_count
        })
    
    except Exception as e:
        logger.error(f"Error cleaning up backups via API: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@backup_bp.route('/api/status')
@admin_required
@function_permission_required('backup_management')
def api_backup_status():
    """API endpoint to get backup system status"""
    try:
        backup_dir = os.getenv('BACKUP_DIR', './backups')
        retention_days = int(os.getenv('BACKUP_RETENTION_DAYS', '30'))
        
        # Get backup list
        backups = list_backups(backup_dir)
        
        # Calculate statistics
        total_size = sum(backup['size'] for backup in backups)
        total_count = len(backups)
        
        # Get disk usage
        backup_path = Path(backup_dir)
        disk_usage = 0
        if backup_path.exists():
            disk_usage = sum(f.stat().st_size for f in backup_path.rglob('*') if f.is_file())
        
        # Get latest backup info
        latest_backup = None
        if backups:
            latest_backup = backups[0]
        
        return jsonify({
            'success': True,
            'status': {
                'total_backups': total_count,
                'total_size': total_size,
                'total_size_formatted': f"{total_size / (1024*1024):.2f} MB",
                'disk_usage': disk_usage,
                'disk_usage_formatted': f"{disk_usage / (1024*1024):.2f} MB",
                'retention_days': retention_days,
                'backup_dir': backup_dir,
                'latest_backup': latest_backup
            }
        })
    
    except Exception as e:
        logger.error(f"Error getting backup status: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500 