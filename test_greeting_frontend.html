<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Greeting System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #greeting-result {
            min-height: 100px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <h1>Greeting System Frontend Test</h1>
    
    <div class="test-container">
        <h2>Test Greeting API</h2>
        <p>This test will call the greeting API endpoint directly to verify it's working.</p>
        
        <div>
            <label for="test-name">Test Name:</label>
            <input type="text" id="test-name" value="TestUser" style="margin: 0 10px; padding: 5px;">
            <button onclick="testGreetingAPI()">Test Greeting API</button>
        </div>
        
        <div id="greeting-result">
            <em>Click the button above to test the greeting API...</em>
        </div>
    </div>

    <div class="test-container">
        <h2>Test Instructions</h2>
        <div class="info">
            <strong>To test the full greeting system:</strong>
            <ol>
                <li>Make sure the Flask application is running (python app/__main__.py)</li>
                <li>Open the main application at <a href="http://localhost:5000" target="_blank">http://localhost:5000</a></li>
                <li>Enter your name in the welcome modal</li>
                <li>Check if you see a personalized greeting instead of the generic "Hello [name]!" message</li>
                <li>The greeting should be contextual (e.g., "Good morning [name]!" in the morning)</li>
            </ol>
        </div>
    </div>

    <script>
        async function testGreetingAPI() {
            const resultDiv = document.getElementById('greeting-result');
            const testName = document.getElementById('test-name').value.trim() || 'TestUser';
            
            resultDiv.innerHTML = '<div class="info">Testing greeting API...</div>';
            
            try {
                const response = await fetch('/api/greeting', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        client_name: testName,
                        context: {
                            device_fingerprint: 'test-device-' + Date.now(),
                            time_of_day: getTimeOfDay(),
                            is_new_session: true
                        }
                    })
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <strong>✓ Greeting API Test Successful!</strong><br>
                            <strong>Greeting:</strong> ${result.greeting}<br>
                            <strong>Template Type:</strong> ${result.greeting_data?.template_type || 'unknown'}<br>
                            <strong>Source:</strong> ${result.greeting_data?.source || 'unknown'}<br>
                            <strong>Time of Day:</strong> ${result.greeting_data?.time_of_day || 'unknown'}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <strong>✗ API Error:</strong> ${result.error || 'Unknown error'}<br>
                            <strong>Fallback Greeting:</strong> ${result.greeting || 'None'}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <strong>✗ Network Error:</strong> ${error.message}<br>
                        <em>Make sure the Flask application is running on localhost:5000</em>
                    </div>
                `;
            }
        }
        
        function getTimeOfDay() {
            const hour = new Date().getHours();
            if (hour < 12) {
                return 'morning';
            } else if (hour < 18) {
                return 'afternoon';
            } else {
                return 'evening';
            }
        }
        
        // Auto-test on page load if running from the Flask server
        if (window.location.hostname === 'localhost' && window.location.port === '5000') {
            window.addEventListener('load', () => {
                setTimeout(testGreetingAPI, 1000);
            });
        }
    </script>
</body>
</html>
