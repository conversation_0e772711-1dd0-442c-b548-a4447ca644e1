#!/usr/bin/env python3
"""
Geolocation Setup Script for AI Analytics System

This script helps set up the geolocation functionality by:
1. Guiding users through MaxMind account setup
2. Configuring environment variables
3. Testing the geolocation service
4. Generating realistic test data for development
"""

import os
import sys
import sqlite3
import uuid
import random
from datetime import datetime, <PERSON><PERSON><PERSON>

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

def print_maxmind_setup_instructions():
    """Print instructions for setting up MaxMind GeoLite2 service."""
    print("🌍 MaxMind GeoLite2 Setup Instructions")
    print("=" * 50)
    print()
    print("To enable real IP geolocation, you need a free MaxMind account:")
    print()
    print("1. 📝 Sign up for a free account:")
    print("   https://www.maxmind.com/en/geolite2/signup")
    print()
    print("2. 🔑 Generate a license key:")
    print("   - Log into your MaxMind account")
    print("   - Go to: https://www.maxmind.com/en/accounts/current/license-key")
    print("   - Click 'Generate new license key'")
    print("   - Choose 'GeoLite2' for the product")
    print("   - Copy your Account ID and License Key")
    print()
    print("3. ⚙️  Update your .env file:")
    print("   MAXMIND_ACCOUNT_ID=your_actual_account_id")
    print("   MAXMIND_LICENSE_KEY=your_actual_license_key")
    print()
    print("4. 🧪 Run this script again to test the configuration")
    print()

def update_env_file(account_id, license_key):
    """Update .env file with MaxMind credentials."""
    env_path = '.env'
    
    if not os.path.exists(env_path):
        print(f"❌ .env file not found at {env_path}")
        return False
    
    # Read current .env content
    with open(env_path, 'r') as f:
        lines = f.readlines()
    
    # Update MaxMind credentials
    updated_lines = []
    account_updated = False
    license_updated = False
    
    for line in lines:
        if line.startswith('MAXMIND_ACCOUNT_ID='):
            updated_lines.append(f'MAXMIND_ACCOUNT_ID={account_id}\n')
            account_updated = True
        elif line.startswith('MAXMIND_LICENSE_KEY='):
            updated_lines.append(f'MAXMIND_LICENSE_KEY={license_key}\n')
            license_updated = True
        else:
            updated_lines.append(line)
    
    # Add missing lines if not found
    if not account_updated:
        updated_lines.append(f'MAXMIND_ACCOUNT_ID={account_id}\n')
    if not license_updated:
        updated_lines.append(f'MAXMIND_LICENSE_KEY={license_key}\n')
    
    # Write updated content
    with open(env_path, 'w') as f:
        f.writelines(updated_lines)
    
    print(f"✅ Updated {env_path} with MaxMind credentials")
    return True

def test_maxmind_credentials():
    """Test MaxMind credentials by making a real API call."""
    print("🧪 Testing MaxMind credentials...")
    
    try:
        from app.services.geo_service import test_geolocation
        
        # Test with Google DNS IP
        result = test_geolocation("*******")
        
        if result.get('latitude') and result.get('longitude'):
            print("✅ MaxMind credentials working!")
            print(f"   Test result: {result.get('city', 'Unknown')}, {result.get('country', 'Unknown')}")
            return True
        else:
            print("❌ MaxMind credentials not working")
            print(f"   Result: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing MaxMind: {str(e)}")
        return False

def generate_realistic_test_data():
    """Generate realistic test geolocation data for development and demonstration."""
    print("🎭 Generating realistic test data for development...")
    
    # Realistic test locations with actual coordinates
    test_locations = [
        # Major cities around the world
        ("*******", "Mountain View", "California", "United States", 37.4056, -122.0775),
        ("*******", "San Francisco", "California", "United States", 37.7749, -122.4194),
        ("**************", "San Francisco", "California", "United States", 37.7749, -122.4194),
        ("*************", "Amsterdam", "North Holland", "Netherlands", 52.3676, 4.9041),
        ("**********", "Frankfurt", "Hesse", "Germany", 50.1109, 8.6821),
        ("*********", "Moscow", "Moscow", "Russia", 55.7558, 37.6176),
        ("************", "Beijing", "Beijing", "China", 39.9042, 116.4074),
        ("************", "Beijing", "Beijing", "China", 39.9042, 116.4074),
        ("**********", "Taipei", "Taiwan", "Taiwan", 25.0330, 121.5654),
        ("*************", "Seoul", "Seoul", "South Korea", 37.5665, 126.9780),
        ("**************", "Tokyo", "Tokyo", "Japan", 35.6762, 139.6503),
        ("***************", "Singapore", "Singapore", "Singapore", 1.3521, 103.8198),
        ("************", "Sydney", "New South Wales", "Australia", -33.8688, 151.2093),
        ("***********", "Cairo", "Cairo", "Egypt", 30.0444, 31.2357),
        ("***********", "Lagos", "Lagos", "Nigeria", 6.5244, 3.3792),
        ("************", "Johannesburg", "Gauteng", "South Africa", -26.2041, 28.0473),
        ("***********", "São Paulo", "São Paulo", "Brazil", -23.5505, -46.6333),
        ("**************", "Buenos Aires", "Buenos Aires", "Argentina", -34.6118, -58.3960),
        ("************", "Mexico City", "Mexico City", "Mexico", 19.4326, -99.1332),
        ("*******", "Toronto", "Ontario", "Canada", 43.6532, -79.3832),
    ]
    
    try:
        from app.services.geo_analytics import save_geoip_data
        
        # Generate data for the past 30 days
        base_date = datetime.now() - timedelta(days=30)
        
        success_count = 0
        
        for i, (ip, city, region, country, lat, lon) in enumerate(test_locations):
            # Generate multiple entries for each location to simulate real usage
            for j in range(random.randint(1, 5)):
                # Random timestamp within the past 30 days
                random_days = random.randint(0, 30)
                random_hours = random.randint(0, 23)
                random_minutes = random.randint(0, 59)
                
                timestamp = base_date + timedelta(days=random_days, hours=random_hours, minutes=random_minutes)
                
                device_fp = str(uuid.uuid4())
                session_id = str(uuid.uuid4())
                
                # Vary the client names
                client_names = [
                    f"User from {city}",
                    f"Researcher {chr(65 + (i % 26))}",
                    f"Student {j + 1}",
                    None  # Some anonymous users
                ]
                client_name = random.choice(client_names)
                
                # Vary the pages visited
                pages = ["/", "/query", "/admin/analytics", "/admin/dashboard"]
                page_url = random.choice(pages)
                
                success = save_geoip_data(
                    ip_address=ip,
                    device_fingerprint=device_fp,
                    client_name=client_name,
                    city=city,
                    region=region,
                    country=country,
                    latitude=lat,
                    longitude=lon,
                    user_agent=f"Mozilla/5.0 Test Agent {i}",
                    page_url=page_url,
                    session_id=session_id
                )
                
                if success:
                    success_count += 1
        
        print(f"✅ Generated {success_count} test geolocation records")
        print(f"   Covering {len(test_locations)} different locations worldwide")
        return True
        
    except Exception as e:
        print(f"❌ Error generating test data: {str(e)}")
        return False

def clear_existing_data():
    """Clear existing geolocation data."""
    print("🧹 Clearing existing geolocation data...")
    
    try:
        conn = sqlite3.connect('chat_history.db')
        cursor = conn.cursor()
        
        # Clear geoip_analytics table
        cursor.execute("DELETE FROM geoip_analytics")
        deleted_count = cursor.rowcount
        
        conn.commit()
        conn.close()
        
        print(f"✅ Cleared {deleted_count} existing geolocation records")
        return True
        
    except Exception as e:
        print(f"❌ Error clearing data: {str(e)}")
        return False

def interactive_setup():
    """Interactive setup process."""
    print("🚀 Interactive Geolocation Setup")
    print("=" * 40)
    print()
    
    print("Choose an option:")
    print("1. 🌍 Set up real MaxMind credentials")
    print("2. 🎭 Generate test data for development")
    print("3. 🧪 Test current configuration")
    print("4. 📖 Show setup instructions")
    print("5. 🧹 Clear existing data")
    print()
    
    choice = input("Enter your choice (1-5): ").strip()
    
    if choice == "1":
        print("\n📝 MaxMind Credentials Setup")
        print("-" * 30)
        account_id = input("Enter your MaxMind Account ID: ").strip()
        license_key = input("Enter your MaxMind License Key: ").strip()
        
        if account_id and license_key:
            if update_env_file(account_id, license_key):
                print("\n🧪 Testing credentials...")
                if test_maxmind_credentials():
                    print("🎉 Setup complete! Real geolocation is now working.")
                else:
                    print("❌ Credentials test failed. Please check your Account ID and License Key.")
        else:
            print("❌ Invalid input. Please provide both Account ID and License Key.")
    
    elif choice == "2":
        print("\n🎭 Test Data Generation")
        print("-" * 25)
        
        # Ask if user wants to clear existing data first
        clear_first = input("Clear existing geolocation data first? (y/n): ").lower().strip()
        if clear_first == 'y':
            clear_existing_data()
        
        if generate_realistic_test_data():
            print("🎉 Test data generated! Check the analytics dashboard to see the map.")
    
    elif choice == "3":
        print("\n🧪 Testing Configuration")
        print("-" * 25)
        test_maxmind_credentials()
    
    elif choice == "4":
        print()
        print_maxmind_setup_instructions()
    
    elif choice == "5":
        print("\n🧹 Clearing Data")
        print("-" * 16)
        confirm = input("Are you sure you want to clear all geolocation data? (y/n): ").lower().strip()
        if confirm == 'y':
            clear_existing_data()
    
    else:
        print("❌ Invalid choice. Please run the script again.")

def main():
    """Main setup function."""
    print("🌍 AI Analytics Geolocation Setup Tool")
    print("=" * 45)
    print()
    
    # Check if running in interactive mode
    if len(sys.argv) > 1 and sys.argv[1] == "--auto-test-data":
        # Automatic test data generation
        print("🎭 Generating test data automatically...")
        clear_existing_data()
        generate_realistic_test_data()
    else:
        # Interactive mode
        interactive_setup()

if __name__ == "__main__":
    main()
