"""
Chat models for sessions and messages.
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from dataclasses import dataclass

@dataclass
class ChatSession:
    """Model for chat sessions."""
    id: Optional[int] = None
    session_id: str = ""
    user_id: Optional[int] = None
    client_name: str = ""
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    started_at: datetime = None
    last_activity: datetime = None
    is_active: bool = True
    created_at: datetime = None
    
    def __post_init__(self):
        if self.started_at is None:
            self.started_at = datetime.now()
        if self.last_activity is None:
            self.last_activity = datetime.now()
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class ChatMessage:
    """Model for individual chat messages."""
    id: Optional[int] = None
    session_id: str = ""
    message_type: str = "user"  # user, assistant, system
    content: str = ""
    timestamp: datetime = None
    metadata: Optional[Dict[str, Any]] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class ChatHistory:
    """Model for chat history entries."""
    id: Optional[int] = None
    session_id: str = ""
    user_message: str = ""
    assistant_response: str = ""
    category: str = ""
    timestamp: datetime = None
    response_time: Optional[float] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.created_at is None:
            self.created_at = datetime.now() 