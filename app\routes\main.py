import os
from flask import Blueprint, render_template, redirect, url_for
from app.utils.helpers import list_categories

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    categories = sorted(list_categories())
    return render_template('index.html', categories=categories)

@main_bp.route('/admin', methods=['GET'])
def admin_redirect():
    """Redirect /admin to /admin/dashboard"""
    return redirect(url_for('admin.admin_dashboard'))
