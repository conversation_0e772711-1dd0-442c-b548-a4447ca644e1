"""
Models package for the ERDB AI application.
"""

# User models
from .user import User, PermissionGroup, GroupPermission

# Content models
from .content import PDFDocument, URLContent, SourceURL, CoverImage

# Chat models
from .chat import ChatSession, ChatMessage, ChatHistory

# Greeting models
from .greeting import GreetingTemplate, UserGreetingPreference, GreetingAnalytics

# Location models
from .location import ExtractedLocation, LocationSource, GeocodingCache

# Schema utilities
from .schema import initialize_database, migrate_location_schema

__all__ = [
    # User models
    'User', 'PermissionGroup', 'GroupPermission',
    
    # Content models
    'PDFDocument', 'URLContent', 'SourceURL', 'CoverImage',
    
    # Chat models
    'ChatSession', 'ChatMessage', 'ChatHistory',
    
    # Greeting models
    'GreetingTemplate', 'UserGreetingPreference', 'GreetingAnalytics',
    
    # Location models
    'ExtractedLocation', 'LocationSource', 'GeocodingCache',
    
    # Schema utilities
    'initialize_database', 'migrate_location_schema'
] 