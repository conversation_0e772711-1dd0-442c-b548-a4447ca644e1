"""
Caching service for ERDB Document Management System
Enhanced with Redis support for distributed caching
"""

import json
import hashlib
import os
from datetime import datetime, timedelta
from typing import Any, Optional, Dict, List
from functools import wraps
import logging

logger = logging.getLogger(__name__)

# Redis imports with fallback to in-memory cache
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logger.warning("Redis not available, falling back to in-memory cache")

class CacheService:
    """Centralized caching service with Redis support"""

    def __init__(self, use_redis: bool = True):
        self._cache = {}
        self._cache_metadata = {}
        self._default_ttl = 3600  # 1 hour default
        self._redis_client = None
        self._use_redis = use_redis and REDIS_AVAILABLE
        self._cache_hits = 0
        self._cache_misses = 0

        # Initialize Redis connection if available
        if self._use_redis:
            try:
                redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379/1')  # Use DB 1 for cache
                self._redis_client = redis.from_url(redis_url, decode_responses=True)
                # Test connection
                self._redis_client.ping()
                logger.info("Redis cache initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize Redis cache: {e}, falling back to in-memory")
                self._use_redis = False
                self._redis_client = None
    
    def _generate_key(self, prefix: str, *args, **kwargs) -> str:
        """Generate cache key from prefix and arguments"""
        key_data = f"{prefix}:{args}:{sorted(kwargs.items())}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache (Redis or in-memory)"""
        try:
            if self._use_redis and self._redis_client:
                # Try Redis first
                cached_data = self._redis_client.get(key)
                if cached_data:
                    self._cache_hits += 1
                    return json.loads(cached_data)
                else:
                    self._cache_misses += 1
                    return None
            else:
                # Fallback to in-memory cache
                if key not in self._cache:
                    self._cache_misses += 1
                    return None

                # Check if expired
                if key in self._cache_metadata:
                    expiry = self._cache_metadata[key].get('expiry')
                    if expiry and datetime.now() > expiry:
                        self.delete(key)
                        self._cache_misses += 1
                        return None

                self._cache_hits += 1
                return self._cache[key]
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            self._cache_misses += 1
            return None
    
    def set(self, key: str, value: Any, ttl: int = None) -> None:
        """Set value in cache with TTL (Redis or in-memory)"""
        try:
            ttl = ttl or self._default_ttl

            if self._use_redis and self._redis_client:
                # Store in Redis with TTL
                serialized_value = json.dumps(value, default=str)
                self._redis_client.setex(key, ttl, serialized_value)
            else:
                # Fallback to in-memory cache
                self._cache[key] = value
                expiry = datetime.now() + timedelta(seconds=ttl)

                self._cache_metadata[key] = {
                    'created': datetime.now(),
                    'expiry': expiry,
                    'ttl': ttl
                }
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            # Fallback to in-memory even if Redis fails
            if not self._use_redis:
                self._cache[key] = value
                expiry = datetime.now() + timedelta(seconds=ttl)
                self._cache_metadata[key] = {
                    'created': datetime.now(),
                    'expiry': expiry,
                    'ttl': ttl
                }
    
    def delete(self, key: str) -> None:
        """Delete key from cache (Redis or in-memory)"""
        try:
            if self._use_redis and self._redis_client:
                self._redis_client.delete(key)
            else:
                self._cache.pop(key, None)
                self._cache_metadata.pop(key, None)
        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
    
    def clear(self) -> None:
        """Clear all cache (Redis or in-memory)"""
        try:
            if self._use_redis and self._redis_client:
                # Clear only our cache keys (with pattern matching if needed)
                self._redis_client.flushdb()
            else:
                self._cache.clear()
                self._cache_metadata.clear()
        except Exception as e:
            logger.error(f"Cache clear error: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        try:
            if self._use_redis and self._redis_client:
                # Redis stats
                info = self._redis_client.info()
                total_keys = self._redis_client.dbsize()
                return {
                    'backend': 'redis',
                    'total_keys': total_keys,
                    'cache_hits': self._cache_hits,
                    'cache_misses': self._cache_misses,
                    'hit_rate': self._cache_hits / (self._cache_hits + self._cache_misses) if (self._cache_hits + self._cache_misses) > 0 else 0,
                    'memory_usage': info.get('used_memory_human', 'unknown'),
                    'connected_clients': info.get('connected_clients', 0)
                }
            else:
                # In-memory stats
                total_keys = len(self._cache)
                expired_keys = sum(
                    1 for metadata in self._cache_metadata.values()
                    if metadata.get('expiry') and datetime.now() > metadata['expiry']
                )

                return {
                    'backend': 'memory',
                    'total_keys': total_keys,
                    'expired_keys': expired_keys,
                    'active_keys': total_keys - expired_keys,
                    'cache_hits': self._cache_hits,
                    'cache_misses': self._cache_misses,
                    'hit_rate': self._cache_hits / (self._cache_hits + self._cache_misses) if (self._cache_hits + self._cache_misses) > 0 else 0,
                    'cache_size': len(str(self._cache))
                }
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {'error': str(e)}
    
    def cleanup_expired(self) -> int:
        """Remove expired entries and return count of removed items"""
        expired_keys = []
        for key, metadata in self._cache_metadata.items():
            if metadata.get('expiry') and datetime.now() > metadata['expiry']:
                expired_keys.append(key)
        
        for key in expired_keys:
            self.delete(key)
        
        return len(expired_keys)

    def invalidate_pattern(self, pattern: str) -> int:
        """Invalidate cache keys matching a pattern"""
        try:
            if self._use_redis and self._redis_client:
                # Use Redis pattern matching
                keys = self._redis_client.keys(pattern)
                if keys:
                    deleted = self._redis_client.delete(*keys)
                    logger.info(f"Invalidated {deleted} cache keys matching pattern: {pattern}")
                    return deleted
                return 0
            else:
                # In-memory pattern matching
                keys_to_delete = [key for key in self._cache.keys() if self._matches_pattern(key, pattern)]
                for key in keys_to_delete:
                    self.delete(key)
                logger.info(f"Invalidated {len(keys_to_delete)} cache keys matching pattern: {pattern}")
                return len(keys_to_delete)
        except Exception as e:
            logger.error(f"Error invalidating pattern {pattern}: {e}")
            return 0

    def _matches_pattern(self, key: str, pattern: str) -> bool:
        """Simple pattern matching for in-memory cache"""
        # Convert Redis-style pattern to simple matching
        if '*' in pattern:
            parts = pattern.split('*')
            if len(parts) == 2:
                return key.startswith(parts[0]) and key.endswith(parts[1])
        return key == pattern

# Global cache instance
cache_service = CacheService()

def cached(prefix: str, ttl: int = None):
    """Decorator for caching function results"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = cache_service._generate_key(prefix, *args, **kwargs)
            
            # Try to get from cache
            cached_result = cache_service.get(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for {prefix}")
                return cached_result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache_service.set(cache_key, result, ttl)
            logger.debug(f"Cache miss for {prefix}, stored result")
            
            return result
        return wrapper
    return decorator

class ModelCache:
    """Cache for AI model responses"""
    
    @staticmethod
    @cached("model_response", ttl=1800)  # 30 minutes
    def get_model_response(model_name: str, prompt: str, **kwargs) -> str:
        """Get cached model response or generate new one"""
        # This would integrate with your actual model service
        pass
    
    @staticmethod
    @cached("embedding", ttl=86400)  # 24 hours
    def get_embedding(model_name: str, text: str) -> List[float]:
        """Get cached embedding or generate new one"""
        # This would integrate with your actual embedding service
        pass

class DocumentCache:
    """Cache for document processing results"""
    
    @staticmethod
    @cached("pdf_text", ttl=86400)  # 24 hours
    def get_pdf_text(file_path: str) -> str:
        """Get cached PDF text or extract new"""
        # This would integrate with your actual PDF processing
        pass
    
    @staticmethod
    @cached("pdf_images", ttl=86400)  # 24 hours
    def get_pdf_images(file_path: str) -> List[Dict[str, Any]]:
        """Get cached PDF images or extract new"""
        # This would integrate with your actual image extraction
        pass
    
    @staticmethod
    @cached("url_content", ttl=3600)  # 1 hour
    def get_url_content(url: str) -> Dict[str, Any]:
        """Get cached URL content or fetch new"""
        # This would integrate with your actual web scraping
        pass

class QueryCache:
    """Enhanced cache for query results with invalidation support"""

    @staticmethod
    def generate_query_cache_key(category: str, question: str, anti_hallucination_mode: str, model_name: str) -> str:
        """Generate a consistent cache key for query results"""
        # Create a hash of the query parameters for consistent caching
        key_data = f"query:{category}:{question}:{anti_hallucination_mode}:{model_name}"
        return hashlib.md5(key_data.encode()).hexdigest()

    @staticmethod
    def get_cached_query_result(category: str, question: str, anti_hallucination_mode: str, model_name: str) -> Optional[Dict[str, Any]]:
        """Get cached query result if available"""
        cache_key = QueryCache.generate_query_cache_key(category, question, anti_hallucination_mode, model_name)
        return cache_service.get(cache_key)

    @staticmethod
    def cache_query_result(category: str, question: str, anti_hallucination_mode: str, model_name: str, result: Dict[str, Any], ttl: int = 1800) -> None:
        """Cache query result with 30-minute TTL by default"""
        cache_key = QueryCache.generate_query_cache_key(category, question, anti_hallucination_mode, model_name)
        cache_service.set(cache_key, result, ttl)
        logger.info(f"Cached query result for category: {category}, key: {cache_key[:16]}...")

    @staticmethod
    def invalidate_category_cache(category: str) -> int:
        """Invalidate all cached queries for a specific category"""
        pattern = f"*query:{category}:*"
        deleted_count = cache_service.invalidate_pattern(pattern)
        logger.info(f"Invalidated {deleted_count} cached queries for category: {category}")
        return deleted_count

    @staticmethod
    @cached("vector_search", ttl=3600)  # 1 hour
    def get_vector_search(query: str, category: str, k: int = 10) -> List[Dict[str, Any]]:
        """Get cached vector search results or perform new search"""
        # This would integrate with your actual vector search
        pass