"""
HTML Generator Service Module

This module provides functionality to generate customized HTML chat interfaces
with pre-configured settings for AI model, anti-hallucination mode, and document categories.
"""

import os
import logging
import json
import re
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from jinja2 import Template, Environment, FileSystemLoader
from app.utils import helpers as utils

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Constants
FRONTEND_DIR = os.path.join(os.getcwd(), 'frontend')
TEMPLATES_DIR = os.path.join(os.getcwd(), 'templates')
BASE_TEMPLATE = 'index.html'

# Ensure frontend directory exists
os.makedirs(FRONTEND_DIR, exist_ok=True)

def dummy_url_for(endpoint, **values):
    """
    A dummy url_for function for generating static URLs in templates.
    
    Args:
        endpoint: The endpoint name (e.g., 'static', 'query')
        **values: Keyword arguments like filename or category
    
    Returns:
        A string representing the URL
    """
    if endpoint == 'static':
        filename = values.get('filename', '')
        return f"/static/{filename}"
    # Log a warning if an unhandled endpoint is used
    logger.warning(f"dummy_url_for called with unhandled endpoint: {endpoint}")
    return f"/{endpoint}"

class HTMLGeneratorService:
    """Service class for generating customized HTML chat interfaces."""
    
    def __init__(self):
        """Initialize the HTML Generator Service."""
        self.jinja_env = Environment(
            loader=FileSystemLoader(TEMPLATES_DIR),
            autoescape=True
        )
    
    def validate_configuration(self, config: Dict[str, Any]) -> Tuple[bool, str]:
        """
        Validate the HTML generation configuration.
        
        Args:
            config: Configuration dictionary containing:
                - ai_model: Selected AI model name
                - anti_hallucination_mode: Mode (strict, balanced, creative)
                - categories: List of document categories
                - filename: Output filename
                
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            # Validate required fields
            required_fields = ['ai_model', 'anti_hallucination_mode', 'categories', 'filename']
            for field in required_fields:
                if field not in config or not config[field]:
                    return False, f"Missing required field: {field}"
            
            # Validate AI model
            ai_model = config['ai_model'].strip()
            if not ai_model:
                return False, "AI model cannot be empty"
            
            # Validate anti-hallucination mode
            valid_modes = ['strict', 'balanced', 'creative']
            if config['anti_hallucination_mode'] not in valid_modes:
                return False, f"Invalid anti-hallucination mode. Must be one of: {', '.join(valid_modes)}"
            
            # Validate categories
            categories = config['categories']
            if not isinstance(categories, list) or len(categories) == 0:
                return False, "At least one category must be selected"
            
            # Validate available categories
            available_categories = utils.list_categories()
            for category in categories:
                if category not in available_categories:
                    return False, f"Invalid category: {category}"
            
            # Validate filename
            filename = config['filename'].strip()
            if not filename:
                return False, "Filename cannot be empty"
            
            # Ensure filename has .html extension
            if not filename.lower().endswith('.html'):
                config['filename'] = filename + '.html'
            
            # Validate filename format (no special characters except dash and underscore)
            if not re.match(r'^[a-zA-Z0-9_-]+\.html$', config['filename']):
                return False, "Filename can only contain letters, numbers, dashes, and underscores"
            
            # Check if file already exists
            output_path = os.path.join(FRONTEND_DIR, config['filename'])
            if os.path.exists(output_path):
                return False, f"File '{config['filename']}' already exists in frontend directory"
            
            return True, ""
            
        except Exception as e:
            logger.error(f"Error validating configuration: {str(e)}")
            return False, f"Validation error: {str(e)}"
    
    def generate_html(self, config: Dict[str, Any]) -> Tuple[bool, str]:
        """
        Generate a customized HTML chat interface.
        
        Args:
            config: Configuration dictionary
            
        Returns:
            Tuple of (success, message)
        """
        try:
            # Validate configuration
            is_valid, error_msg = self.validate_configuration(config)
            if not is_valid:
                return False, error_msg
            
            # Load the base template
            template = self.jinja_env.get_template(BASE_TEMPLATE)
            
            # Prepare template variables
            template_vars = self._prepare_template_variables(config)
            
            # Render the template
            rendered_html = template.render(**template_vars)
            
            # Post-process the HTML to remove user controls and inject pre-configured settings
            processed_html = self._process_generated_html(rendered_html, config)
            
            # Save the generated HTML
            output_path = os.path.join(FRONTEND_DIR, config['filename'])
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(processed_html)
            
            logger.info(f"Successfully generated HTML interface: {config['filename']}")
            return True, f"HTML interface '{config['filename']}' generated successfully"
            
        except Exception as e:
            logger.error(f"Error generating HTML: {str(e)}")
            return False, f"Generation failed: {str(e)}"
    
    def _prepare_template_variables(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare variables for template rendering.
        
        Args:
            config: Configuration dictionary
            
        Returns:
            Dictionary of template variables
        """
        # Get available categories (all categories for the base template)
        all_categories = utils.list_categories()
        
        # Prepare template variables similar to the index route
        template_vars = {
            'categories': all_categories,
            'generated_config': config,  # Pass config for processing
            'generation_timestamp': datetime.now().isoformat(),
            'is_generated_interface': True,
            'url_for':dummy_url_for,
            'csrf_token': lambda: ''  # Dummy csrf_token function to prevent rendering errors
        }
        
        return template_vars
    
    # def _process_generated_html(self, html: str, config: Dict[str, Any]) -> str:
        # """
        # Post-process the generated HTML to remove user controls and inject settings.

        # Args:
            # html: Raw HTML content
            # config: Configuration dictionary

        # Returns:
            # Processed HTML content
        # """
        # try:
            # # Remove category selector and replace with hidden input
            # selected_categories = config['categories']
            # if len(selected_categories) == 1:
                # # Single category - replace selector with hidden input
                # category_replacement = f'''
                    # <input type="hidden" id="category" value="{selected_categories[0]}">
                    # <div class="input-group">
                        # <label class="input-label">Selected Category</label>
                        # <div class="selected-category-display">
                            # <span class="badge bg-primary">{selected_categories[0]}</span>
                        # </div>
                    # </div>'''
            # else:
                # # Multiple categories - show as read-only badges
                # category_badges = ' '.join([f'<span class="badge bg-primary me-1">{cat}</span>' for cat in selected_categories])
                # category_replacement = f'''
                    # <input type="hidden" id="category" value="{selected_categories[0]}">
                    # <div class="input-group">
                        # <label class="input-label">Selected Categories</label>
                        # <div class="selected-category-display">
                            # {category_badges}
                        # </div>
                    # </div>'''

            # # Replace category selector
            # html = re.sub(
                # r'<select id="category"[^>]*>.*?</select>',
                # category_replacement,
                # html,
                # flags=re.DOTALL
            # )

            # # Remove anti-hallucination mode selector and inject pre-configured value
            # mode_value = config['anti_hallucination_mode']
            # mode_replacement = f'''
                # <input type="hidden" name="anti_hallucination_mode" value="{mode_value}">
                # <div class="input-group">
                    # <label class="input-label">Response Mode</label>
                    # <div class="selected-mode-display">
                        # <span class="badge bg-secondary">{mode_value.title()}</span>
                    # </div>
                # </div>'''

            # # Replace anti-hallucination mode selector (look for the radio button container)
            # html = re.sub(
                # r'<div class="mode-selector">.*?</div>',
                # mode_replacement,
                # html,
                # flags=re.DOTALL
            # )

            # # Also remove the radio button group if it exists
            # html = re.sub(
                # r'<div class="input-group">\s*<label[^>]*>Anti-Hallucination Mode</label>.*?</div>',
                # mode_replacement,
                # html,
                # flags=re.DOTALL
            # )

            # # Remove model selector dropdown and replace with display
            # ai_model = config['ai_model']
            # model_display_name = ai_model.replace(':', ' ').replace('-', ' ').title()
            # model_replacement = f'''
                # <div class="input-group">
                    # <label class="input-label">AI Model</label>
                    # <div class="selected-model-display">
                        # <span class="badge bg-info">{model_display_name}</span>
                    # </div>
                # </div>'''

            # # Replace model selector
            # html = re.sub(
                # r'<div class="model-selector">.*?</div>',
                # model_replacement,
                # html,
                # flags=re.DOTALL
            # )

            # # Inject pre-configured settings and modify sendQuery function
            # ai_model = config['ai_model']
            # model_injection = f'''
                # <script>
                    # // Pre-configured settings for generated interface
                    # window.GENERATED_CONFIG = {{
                        # aiModel: '{ai_model}',
                        # antiHallucinationMode: '{mode_value}',
                        # categories: {json.dumps(selected_categories)},
                        # isGenerated: true
                    # }};

                    # // Override model selection and sendQuery function
                    # document.addEventListener('DOMContentLoaded', function() {{
                        # selectedModel = '{ai_model}';
                        # localStorage.setItem('selectedModel', selectedModel);

                        # // Update model info if function exists
                        # if (typeof updateModelInfo === 'function') {{
                            # updateModelInfo();
                        # }}

                        # // Override sendQuery function for multiple categories
                        # if (window.GENERATED_CONFIG.categories.length > 1) {{
                            # const originalSendQuery = window.sendQuery;
                            # window.sendQuery = async function() {{
                                # const query = document.getElementById('query').value.trim();
                                # if (!query) {{
                                    # alert('Please enter a query.');
                                    # return;
                                # }}

                                # // Use the first category for the query endpoint
                                # const category = window.GENERATED_CONFIG.categories[0];
                                # const antiHallucinationMode = window.GENERATED_CONFIG.antiHallucinationMode;
                                # const selectedModel = window.GENERATED_CONFIG.aiModel;

                                # // Call the original function logic with pre-configured values
                                # return originalSendQuery.call(this);
                            # }};
                        # }}
                    # }});
                # </script>'''
            # # Inject the configuration script before closing head tag
            # #html = html.replace('</head>', f'{model_injection}\n</head>')
            # html = html.replace('</head>', f'{model_injection}\n{csrf_token_script}\n</head>')
            # # Add generation metadata comment
            # metadata_comment = f'''
# <!--
# Generated HTML Interface
# Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# AI Model: {ai_model}
# Anti-hallucination Mode: {mode_value}
# Categories: {', '.join(selected_categories)}
# -->
# '''
            # html = metadata_comment + html

            # return html

        # except Exception as e:
            # logger.error(f"Error processing generated HTML: {str(e)}")
            # raise
    def _process_generated_html(self, html: str, config: Dict[str, Any]) -> str:
        """
        Post-process the generated HTML to remove user controls and inject settings.

        Args:
            html: Raw HTML content
            config: Configuration dictionary

        Returns:
            Processed HTML content
        """
        try:
            # Remove category selector and replace with hidden input
            selected_categories = config['categories']
            if len(selected_categories) == 1:
                category_replacement = f'''
                    <input type="hidden" id="category" value="{selected_categories[0]}">
                    <div class="input-group">
                        <label class="input-label">Selected Category</label>
                        <div class="selected-category-display">
                            <span class="badge bg-primary">{selected_categories[0]}</span>
                        </div>
                    </div>'''
            else:
                category_badges = ' '.join(
                    f'<span class="badge bg-primary me-1">{cat}</span>' 
                    for cat in selected_categories
                )
                category_replacement = f'''
                    <input type="hidden" id="category" value="{selected_categories[0]}">
                    <div class="input-group">
                        <label class="input-label">Selected Categories</label>
                        <div class="selected-category-display">
                            {category_badges}
                        </div>
                    </div>'''

            html = re.sub(
                r'<select id="category"[^>]*>.*?</select>',
                category_replacement,
                html,
                flags=re.DOTALL
            )

            # Remove anti-hallucination mode selector and inject pre-configured value
            mode_value = config['anti_hallucination_mode']
            mode_replacement = f'''
                <input type="hidden" name="anti_hallucination_mode" value="{mode_value}">
                <div class="input-group">
                    <label class="input-label">Response Mode</label>
                    <div class="selected-mode-display">
                        <span class="badge bg-secondary">{mode_value.title()}</span>
                    </div>
                </div>'''

            html = re.sub(
                r'<div class="mode-selector">.*?</div>',
                mode_replacement,
                html,
                flags=re.DOTALL
            )
            html = re.sub(
                r'<div class="input-group">\s*<label[^>]*>Anti-Hallucination Mode</label>.*?</div>',
                mode_replacement,
                html,
                flags=re.DOTALL
            )

            # Remove model selector dropdown and replace with display
            ai_model = config['ai_model']
            model_display_name = ai_model.replace(':', ' ').replace('-', ' ').title()
            model_replacement = f'''
                <div class="input-group">
                    <label class="input-label">AI Model</label>
                    <div class="selected-model-display">
                        <span class="badge bg-info">{model_display_name}</span>
                    </div>
                </div>'''

            html = re.sub(
                r'<div class="model-selector">.*?</div>',
                model_replacement,
                html,
                flags=re.DOTALL
            )

            # Inject pre-configured settings and modify sendQuery function
            model_injection = f'''
                <script>
                    // Pre-configured settings for generated interface
                    window.GENERATED_CONFIG = {{
                        aiModel: '{ai_model}',
                        antiHallucinationMode: '{mode_value}',
                        categories: {json.dumps(selected_categories)},
                        isGenerated: true
                    }};

                    document.addEventListener('DOMContentLoaded', function() {{
                        selectedModel = '{ai_model}';
                        localStorage.setItem('selectedModel', selectedModel);

                        if (typeof updateModelInfo === 'function') {{
                            updateModelInfo();
                        }}

                        if (window.GENERATED_CONFIG.categories.length > 1) {{
                            const originalSendQuery = window.sendQuery;
                            window.sendQuery = async function() {{
                                const query = document.getElementById('query').value.trim();
                                if (!query) {{
                                    alert('Please enter a query.');
                                    return;
                                }}

                                // Use the first category
                                const category = window.GENERATED_CONFIG.categories[0];
                                const antiHallucinationMode = window.GENERATED_CONFIG.antiHallucinationMode;
                                const selectedModel = window.GENERATED_CONFIG.aiModel;

                                // Call the original function logic
                                return originalSendQuery.call(this);
                            }};
                        }}
                    }});
                </script>'''

            # Only inject the model_injection (remove csrf_token_script)
            html = html.replace('</head>', f'{model_injection}\n</head>')

            # Add generation metadata comment at the very top
            metadata_comment = f'''
<!--
Generated HTML Interface
Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
AI Model: {ai_model}
Anti-hallucination Mode: {mode_value}
Categories: {', '.join(selected_categories)}
-->
'''
            html = metadata_comment + html

            return html

        except Exception as e:
            logger.error(f"Error processing generated HTML: {str(e)}")
            raise

    
    def list_generated_files(self) -> List[Dict[str, Any]]:
        """
        List all generated HTML files in the frontend directory.
        
        Returns:
            List of file information dictionaries
        """
        try:
            files = []
            if os.path.exists(FRONTEND_DIR):
                for filename in os.listdir(FRONTEND_DIR):
                    if filename.endswith('.html'):
                        filepath = os.path.join(FRONTEND_DIR, filename)
                        stat = os.stat(filepath)
                        
                        files.append({
                            'filename': filename,
                            'size': stat.st_size,
                            'created': datetime.fromtimestamp(stat.st_ctime).strftime('%Y-%m-%d %H:%M:%S'),
                            'modified': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                        })
            
            return sorted(files, key=lambda x: x['modified'], reverse=True)
            
        except Exception as e:
            logger.error(f"Error listing generated files: {str(e)}")
            return []
    
    def delete_generated_file(self, filename: str) -> Tuple[bool, str]:
        """
        Delete a generated HTML file.
        
        Args:
            filename: Name of the file to delete
            
        Returns:
            Tuple of (success, message)
        """
        try:
            if not filename.endswith('.html'):
                return False, "Invalid file type"
            
            filepath = os.path.join(FRONTEND_DIR, filename)
            if not os.path.exists(filepath):
                return False, "File not found"
            
            os.remove(filepath)
            logger.info(f"Deleted generated HTML file: {filename}")
            return True, f"File '{filename}' deleted successfully"
            
        except Exception as e:
            logger.error(f"Error deleting file {filename}: {str(e)}")
            return False, f"Failed to delete file: {str(e)}"


# Global service instance
html_generator = HTMLGeneratorService()
