"""
Adaptive Chunking Optimization Service

This module provides dynamic chunking parameter optimization based on document
characteristics such as paragraph length, table complexity, column layout, and document size.
"""

import os
import logging
from typing import Dict, List, Any, Optional
import fitz  # PyMuPDF
from config.chunking_config import ChunkingConfig
from app.services.pdf_processor import detect_multi_column_layout, extract_enhanced_text_with_positioning
from app.utils.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)


class AdaptiveChunkingOptimizer:
    """Optimize chunking parameters based on document characteristics"""

    def __init__(self):
        self.base_config = ChunkingConfig.from_json_file()
        self.optimization_rules = {
            'long_paragraphs': {
                'threshold': 500,
                'chunk_size_multiplier': 1.3,
                'overlap_multiplier': 1.2,
                'max_chunk_size': 1500
            },
            'complex_tables': {
                'force_semantic': True,
                'strategy': 'semantic',
                'chunk_size_multiplier': 1.1
            },
            'multi_column': {
                'preserve_column_order': True,
                'overlap_multiplier': 1.5,
                'min_overlap': 300
            },
            'large_document': {
                'page_threshold': 50,
                'enable_parallel': True,
                'batch_size_multiplier': 2,
                'max_batch_size': 20
            },
            'technical_content': {
                'chunk_size_multiplier': 1.2,
                'use_semantic': True,
                'strategy': 'semantic'
            }
        }

    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True)
    def optimize_for_document(self, pdf_path: str, content_analysis: Dict = None) -> Dict:
        """
        Generate optimized chunking config for specific document

        Args:
            pdf_path: Path to the PDF file
            content_analysis: Pre-analyzed content information

        Returns:
            Optimized chunking configuration
        """
        try:
            # Analyze document characteristics
            doc_stats = self._analyze_document_stats(pdf_path)

            # Get base configuration
            content_type = content_analysis.get('content_type', 'general') if content_analysis else 'general'
            config = self.base_config.get_config_for_content_type(content_type).copy()

            logger.info(f"Base config for {content_type}: chunk_size={config.get('chunk_size', 800)}")

            # Apply optimization rules
            config = self._apply_optimization_rules(config, doc_stats, content_type)

            # Validate and constrain final configuration
            config = self._validate_and_constrain_config(config)

            logger.info(f"Optimized config: chunk_size={config.get('chunk_size')}, "
                       f"overlap={config.get('chunk_overlap')}, strategy={config.get('strategy')}")

            return config

        except Exception as e:
            logger.error(f"Error optimizing chunking config: {e}")
            # Return base configuration as fallback
            return self.base_config.get_config_for_content_type('general')

    def _analyze_document_stats(self, pdf_path: str) -> Dict:
        """Analyze document statistics for optimization"""
        try:
            doc = fitz.open(pdf_path)
            stats = {
                'total_pages': len(doc),
                'avg_paragraph_length': 0,
                'has_complex_tables': False,
                'column_count': 1,
                'text_density': 0,
                'technical_term_density': 0,
                'avg_line_length': 0,
                'has_equations': False,
                'has_code_blocks': False
            }

            # Sample analysis on first few pages
            sample_pages = min(5, len(doc))
            total_text_length = 0
            paragraph_lengths = []
            line_lengths = []
            technical_indicators = 0

            for page_num in range(sample_pages):
                page = doc[page_num]
                text = page.get_text()

                # Analyze paragraphs
                paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
                paragraph_lengths.extend([len(p) for p in paragraphs])

                # Analyze lines
                lines = [line.strip() for line in text.split('\n') if line.strip()]
                line_lengths.extend([len(line) for line in lines])

                total_text_length += len(text)

                # Detect technical content indicators
                technical_indicators += self._count_technical_indicators(text)

                # Detect complex tables
                if self._detect_complex_tables(page):
                    stats['has_complex_tables'] = True

                # Detect columns
                try:
                    enhanced_items = extract_enhanced_text_with_positioning(page)
                    layout_info = detect_multi_column_layout(enhanced_items)
                    if layout_info['is_multi_column']:
                        stats['column_count'] = max(stats['column_count'],
                                                  layout_info.get('column_count', 1))
                except Exception as e:
                    logger.warning(f"Error detecting columns on page {page_num}: {e}")

            # Calculate statistics
            stats['avg_paragraph_length'] = (
                sum(paragraph_lengths) / len(paragraph_lengths)
                if paragraph_lengths else 0
            )
            stats['avg_line_length'] = (
                sum(line_lengths) / len(line_lengths)
                if line_lengths else 0
            )
            stats['text_density'] = total_text_length / sample_pages
            stats['technical_term_density'] = technical_indicators / sample_pages

            # Detect equations and code blocks
            stats['has_equations'] = self._detect_equations(text)
            stats['has_code_blocks'] = self._detect_code_blocks(text)

            doc.close()
            logger.info(f"Document stats: {stats}")
            return stats

        except Exception as e:
            logger.error(f"Error analyzing document stats: {e}")
            return {
                'total_pages': 1,
                'avg_paragraph_length': 300,
                'has_complex_tables': False,
                'column_count': 1,
                'text_density': 1000,
                'technical_term_density': 0,
                'avg_line_length': 50,
                'has_equations': False,
                'has_code_blocks': False
            }

    def _apply_optimization_rules(self, config: Dict, doc_stats: Dict, content_type: str) -> Dict:
        """Apply optimization rules based on document characteristics"""

        # Rule 1: Long paragraphs
        if doc_stats['avg_paragraph_length'] > self.optimization_rules['long_paragraphs']['threshold']:
            logger.info("Applying long paragraphs optimization")
            multiplier = self.optimization_rules['long_paragraphs']['chunk_size_multiplier']
            config['chunk_size'] = min(
                int(config.get('chunk_size', 800) * multiplier),
                self.optimization_rules['long_paragraphs']['max_chunk_size']
            )
            config['chunk_overlap'] = int(
                config.get('chunk_overlap', 200) *
                self.optimization_rules['long_paragraphs']['overlap_multiplier']
            )

        # Rule 2: Complex tables
        if doc_stats['has_complex_tables']:
            logger.info("Applying complex tables optimization")
            config['use_semantic'] = True
            config['strategy'] = 'semantic'
            config['chunk_size'] = int(
                config.get('chunk_size', 800) *
                self.optimization_rules['complex_tables']['chunk_size_multiplier']
            )

        # Rule 3: Multi-column layout
        if doc_stats['column_count'] > 1:
            logger.info(f"Applying multi-column optimization for {doc_stats['column_count']} columns")
            config['preserve_column_order'] = True
            config['chunk_overlap'] = max(
                int(config.get('chunk_overlap', 200) *
                    self.optimization_rules['multi_column']['overlap_multiplier']),
                self.optimization_rules['multi_column']['min_overlap']
            )

        # Rule 4: Large documents
        if doc_stats['total_pages'] > self.optimization_rules['large_document']['page_threshold']:
            logger.info("Applying large document optimization")
            config['enable_parallel_processing'] = True
            config['batch_size'] = min(
                int(config.get('batch_size', 10) *
                    self.optimization_rules['large_document']['batch_size_multiplier']),
                self.optimization_rules['large_document']['max_batch_size']
            )

        # Rule 5: Technical content
        if (content_type in ['technical', 'scientific'] or
            doc_stats['technical_term_density'] > 2 or
            doc_stats['has_equations'] or
            doc_stats['has_code_blocks']):
            logger.info("Applying technical content optimization")
            config['use_semantic'] = True
            config['strategy'] = 'semantic'
            config['chunk_size'] = int(
                config.get('chunk_size', 800) *
                self.optimization_rules['technical_content']['chunk_size_multiplier']
            )

        return config

    def _validate_and_constrain_config(self, config: Dict) -> Dict:
        """Validate and constrain configuration to reasonable limits"""

        # Constrain chunk size
        config['chunk_size'] = max(100, min(config.get('chunk_size', 800), 2000))

        # Constrain overlap (should be less than chunk size)
        max_overlap = int(config['chunk_size'] * 0.5)  # Max 50% overlap
        config['chunk_overlap'] = max(50, min(config.get('chunk_overlap', 200), max_overlap))

        # Ensure batch size is reasonable
        config['batch_size'] = max(1, min(config.get('batch_size', 10), 50))

        # Validate strategy
        valid_strategies = ['semantic', 'sentence_aware', 'character']
        if config.get('strategy') not in valid_strategies:
            config['strategy'] = 'sentence_aware'

        return config

    def _count_technical_indicators(self, text: str) -> int:
        """Count technical content indicators in text"""
        indicators = [
            'algorithm', 'function', 'method', 'class', 'variable',
            'parameter', 'return', 'import', 'library', 'framework',
            'API', 'HTTP', 'JSON', 'XML', 'SQL', 'database',
            'server', 'client', 'protocol', 'interface'
        ]

        text_lower = text.lower()
        count = sum(text_lower.count(indicator) for indicator in indicators)
        return count

    def _detect_complex_tables(self, page) -> bool:
        """Detect if page contains complex tables"""
        try:
            # Look for table indicators
            text = page.get_text()

            # Count pipe characters (markdown tables)
            pipe_count = text.count('|')

            # Count tab-separated content
            tab_count = text.count('\t')

            # Look for table-like patterns
            lines = text.split('\n')
            table_lines = 0
            for line in lines:
                # Check for multiple columns separated by spaces
                parts = line.split()
                if len(parts) >= 3 and all(len(part) > 0 for part in parts):
                    table_lines += 1

            # Consider it a complex table if multiple indicators are present
            return (pipe_count > 10 or tab_count > 5 or table_lines > 5)

        except Exception as e:
            logger.error(f"Error detecting complex tables: {e}")
            return False

    def _detect_equations(self, text: str) -> bool:
        """Detect mathematical equations in text"""
        equation_indicators = ['=', '∑', '∫', '∂', '∇', '±', '≤', '≥', '≠']
        return any(indicator in text for indicator in equation_indicators)

    def _detect_code_blocks(self, text: str) -> bool:
        """Detect code blocks in text"""
        code_indicators = ['def ', 'class ', 'import ', 'function(', '{', '}', '[];', '()']
        return any(indicator in text for indicator in code_indicators)