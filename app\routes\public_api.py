from flask import Blueprint, request, jsonify, current_app
from app.utils.security import csrf
from app.services.greeting_service import G<PERSON>ingManager

public_api_bp = Blueprint('public_api', __name__)

@public_api_bp.route('/api/greeting', methods=['POST'])
@csrf.exempt
def get_greeting():
    current_app.logger.info("/api/greeting called (public_api.py). Request headers: %s", dict(request.headers))
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "Request data is required"}), 400

        client_name = data.get('client_name')
        if not client_name:
            return jsonify({"error": "Client name is required"}), 400

        context = data.get('context', {})
        greeting_manager = GreetingManager()
        greeting_data = greeting_manager.get_contextual_greeting(client_name, context)
        session_id = context.get('session_id')
        if session_id:
            greeting_manager.log_greeting_usage(session_id, client_name, greeting_data)
        return jsonify({
            "success": True,
            "greeting": greeting_data['greeting'],
            "greeting_data": {
                "template_id": greeting_data.get('template_id'),
                "source": greeting_data.get('source'),
                "template_type": greeting_data.get('template_type'),
                "session_type": greeting_data.get('session_type'),
                "time_of_day": greeting_data.get('time_of_day'),
                "context": greeting_data.get('context', {})
            },
            "metadata": {
                "template_id": greeting_data.get('template_id'),
                "source": greeting_data.get('source'),
                "template_type": greeting_data.get('template_type')
            }
        }), 200
    except Exception as e:
        current_app.logger.error(f"Error getting greeting: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "greeting": f"Hello {data.get('client_name', 'there')}!" if 'data' in locals() else "Hello there!"
        }), 500
