---
const { title, isRtl } = Astro.props
const cssPath = isRtl ? ".rtl" : "";

// Smart path resolution: calculate relative path based on directory depth
const pathname = Astro.url.pathname;
// Only count directories, not the filename itself
const pathSegments = pathname.split('/').filter(segment => segment !== '');
const depth = pathSegments.length > 0 ? pathSegments.length - 1 : 0;
const deploymentPath = depth === 0 ? './' : '../'.repeat(depth);
---

<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>{title}</title>

<!--begin::Accessibility Meta Tags-->
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
<meta name="color-scheme" content="light dark" />
<meta name="theme-color" content="#007bff" media="(prefers-color-scheme: light)" />
<meta name="theme-color" content="#1a1a1a" media="(prefers-color-scheme: dark)" />
<!--end::Accessibility Meta Tags-->

<!--begin::Primary Meta Tags-->
<meta name="title" content={title} />
<meta name="author" content="ColorlibHQ" />
<meta
  name="description"
  content="AdminLTE is a Free Bootstrap 5 Admin Dashboard, 30 example pages using Vanilla JS. Fully accessible with WCAG 2.1 AA compliance."
/>
<meta
  name="keywords"
  content="bootstrap 5, bootstrap, bootstrap 5 admin dashboard, bootstrap 5 dashboard, bootstrap 5 charts, bootstrap 5 calendar, bootstrap 5 datepicker, bootstrap 5 tables, bootstrap 5 datatable, vanilla js datatable, colorlibhq, colorlibhq dashboard, colorlibhq admin dashboard, accessible admin panel, WCAG compliant"
/>
<!--end::Primary Meta Tags-->

<!--begin::Accessibility Features-->
<!-- Skip links will be dynamically added by accessibility.js -->
<meta name="supported-color-schemes" content="light dark" />
<link rel="preload" href={deploymentPath + "css/adminlte" + cssPath + ".css"} as="style" />
<!--end::Accessibility Features-->

<!--begin::Fonts-->
<link
  rel="stylesheet"
  href="https://cdn.jsdelivr.net/npm/@fontsource/source-sans-3@5.0.12/index.css"
  integrity="sha256-tXJfXfp6Ewt1ilPzLDtQnJV4hclT9XuaZUKyUvmyr+Q="
  crossorigin="anonymous"
  media="print"
  onload="this.media='all'"
/>
<!--end::Fonts-->

<!--begin::Third Party Plugin(OverlayScrollbars)-->
<link
  rel="stylesheet"
  href="https://cdn.jsdelivr.net/npm/overlayscrollbars@2.11.0/styles/overlayscrollbars.min.css"
  crossorigin="anonymous"
/>
<!--end::Third Party Plugin(OverlayScrollbars)-->

<!--begin::Third Party Plugin(Bootstrap Icons)-->
<link 
  rel="stylesheet" 
  href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.13.1/font/bootstrap-icons.min.css" 
  crossorigin="anonymous"
/>
<!--end::Third Party Plugin(Bootstrap Icons)-->

<!--begin::Required Plugin(AdminLTE)-->
<link rel="stylesheet" href={deploymentPath + "css/adminlte" + cssPath + ".css"} />
<!--end::Required Plugin(AdminLTE)-->
