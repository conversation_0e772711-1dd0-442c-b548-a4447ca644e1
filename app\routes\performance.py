"""
Performance Dashboard Routes for ERDB System

This module provides web-based performance monitoring and dashboard functionality
including real-time metrics, system health monitoring, and performance analytics.
"""

from flask import Blueprint, render_template, jsonify, request
import json
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List

from app.utils.performance_monitor import get_performance_monitor
from app.utils.chroma_performance import get_chroma_monitor, export_chroma_metrics
from app.utils.health_monitor import get_health_monitor
from app.utils.database_optimizer import get_database_optimizer

# Create blueprint
performance_bp = Blueprint('performance', __name__, url_prefix='/performance')

@performance_bp.route('/')
def dashboard():
    """Main performance dashboard page."""
    return render_template('performance/dashboard.html')

@performance_bp.route('/api/system-metrics')
def get_system_metrics():
    """Get current system metrics."""
    try:
        health_monitor = get_health_monitor()
        system_metrics = health_monitor.get_system_metrics()
        health_status = health_monitor.check_health_status()
        
        return jsonify({
            'success': True,
            'data': {
                'system_metrics': {
                    'timestamp': system_metrics.timestamp,
                    'cpu_percent': system_metrics.cpu_percent,
                    'memory_percent': system_metrics.memory_percent,
                    'memory_available_mb': system_metrics.memory_available_mb,
                    'disk_usage_percent': system_metrics.disk_usage_percent,
                    'disk_free_gb': system_metrics.disk_free_gb,
                    'active_connections': system_metrics.active_connections,
                    'database_size_mb': system_metrics.database_size_mb,
                    'uptime_seconds': system_metrics.uptime_seconds
                },
                'health_status': {
                    'status': health_status.status,
                    'score': health_status.score,
                    'issues': health_status.issues,
                    'recommendations': health_status.recommendations,
                    'last_check': health_status.last_check
                }
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@performance_bp.route('/api/performance-metrics')
def get_performance_metrics():
    """Get performance metrics for functions and operations."""
    try:
        hours = request.args.get('hours', 1, type=int)
        
        perf_monitor = get_performance_monitor()
        recent_metrics = perf_monitor.get_recent_metrics(hours)
        function_stats = perf_monitor.get_function_stats()
        bottlenecks = perf_monitor.get_bottlenecks()
        
        # Convert metrics to JSON-serializable format
        metrics_data = []
        for metric in recent_metrics:
            metrics_data.append({
                'function_name': metric.function_name,
                'module_name': metric.module_name,
                'execution_time': metric.execution_time,
                'memory_before_mb': metric.memory_before_mb,
                'memory_after_mb': metric.memory_after_mb,
                'memory_peak_mb': metric.memory_peak_mb,
                'cpu_percent': metric.cpu_percent,
                'timestamp': metric.timestamp,
                'result_size': metric.result_size,
                'error': metric.error
            })
        
        bottlenecks_data = []
        for bottleneck in bottlenecks:
            bottlenecks_data.append({
                'function_name': bottleneck.function_name,
                'severity': bottleneck.severity,
                'message': bottleneck.message,
                'execution_time': bottleneck.execution_time,
                'memory_usage_mb': bottleneck.memory_usage_mb,
                'timestamp': bottleneck.timestamp,
                'recommendations': bottleneck.recommendations
            })
        
        return jsonify({
            'success': True,
            'data': {
                'recent_metrics': metrics_data,
                'function_statistics': function_stats,
                'bottlenecks': bottlenecks_data,
                'metrics_count': len(metrics_data)
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@performance_bp.route('/api/database-metrics')
def get_database_metrics():
    """Get database performance metrics."""
    try:
        health_monitor = get_health_monitor()
        db_metrics = health_monitor.get_database_metrics()
        
        # Get database optimizer metrics
        db_optimizer = get_database_optimizer()
        table_stats = db_optimizer.get_table_statistics()
        indexes = db_optimizer.analyze_indexes()
        
        db_metrics_data = []
        for metric in db_metrics:
            db_metrics_data.append({
                'database_name': metric.database_name,
                'size_mb': metric.size_mb,
                'page_count': metric.page_count,
                'cache_hit_ratio': metric.cache_hit_ratio,
                'active_connections': metric.active_connections,
                'last_vacuum': metric.last_vacuum,
                'integrity_check': metric.integrity_check
            })
        
        table_stats_data = []
        for stat in table_stats:
            table_stats_data.append({
                'table_name': stat.table_name,
                'row_count': stat.row_count,
                'size_kb': stat.size_kb,
                'index_count': stat.index_count,
                'last_analyzed': stat.last_analyzed
            })
        
        indexes_data = []
        for index in indexes:
            indexes_data.append({
                'name': index.name,
                'table': index.table,
                'columns': index.columns,
                'unique': index.unique,
                'partial': index.partial,
                'size_kb': index.size_kb
            })
        
        return jsonify({
            'success': True,
            'data': {
                'database_metrics': db_metrics_data,
                'table_statistics': table_stats_data,
                'indexes': indexes_data
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@performance_bp.route('/api/chroma-metrics')
def get_chroma_metrics():
    """Get ChromaDB performance metrics."""
    try:
        chroma_monitor = get_chroma_monitor()
        collection_stats = chroma_monitor.get_collection_stats()
        slow_operations = chroma_monitor.get_slow_operations(threshold_seconds=1.0)
        
        slow_ops_data = []
        for op in slow_operations:
            slow_ops_data.append({
                'operation_type': op.operation_type,
                'collection_name': op.collection_name,
                'vector_count': op.vector_count,
                'dimension': op.dimension,
                'execution_time': op.execution_time,
                'similarity_threshold': op.similarity_threshold,
                'top_k': op.top_k,
                'timestamp': op.timestamp,
                'memory_usage_mb': op.memory_usage_mb,
                'error': op.error
            })
        
        return jsonify({
            'success': True,
            'data': {
                'collection_statistics': collection_stats,
                'slow_operations': slow_ops_data
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@performance_bp.route('/api/optimize-database', methods=['POST'])
def optimize_database():
    """Trigger database optimization."""
    try:
        db_optimizer = get_database_optimizer()
        
        # Create recommended indexes
        index_results = db_optimizer.create_recommended_indexes()
        
        # Get suggestions for future optimization
        suggestions = db_optimizer.suggest_indexes()
        
        return jsonify({
            'success': True,
            'data': {
                'index_results': index_results,
                'suggestions': suggestions,
                'message': f"Created {len([r for r in index_results if r['status'] == 'created'])} new indexes"
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@performance_bp.route('/api/export-metrics')
def export_metrics():
    """Export all performance metrics to files."""
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        exports_dir = "./exports/performance"
        os.makedirs(exports_dir, exist_ok=True)
        
        # Export performance metrics
        perf_monitor = get_performance_monitor()
        perf_file = f"{exports_dir}/performance_metrics_{timestamp}.json"
        perf_monitor.export_metrics(perf_file)
        
        # Export ChromaDB metrics
        chroma_file = f"{exports_dir}/chroma_metrics_{timestamp}.json"
        export_chroma_metrics(chroma_file)
        
        # Export health metrics
        health_monitor = get_health_monitor()
        health_file = f"{exports_dir}/health_metrics_{timestamp}.json"
        health_monitor.export_metrics(health_file)
        
        return jsonify({
            'success': True,
            'data': {
                'files': [perf_file, chroma_file, health_file],
                'timestamp': timestamp,
                'message': 'Metrics exported successfully'
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@performance_bp.route('/api/performance-trends')
def get_performance_trends():
    """Get performance trends over time."""
    try:
        hours = request.args.get('hours', 24, type=int)
        
        perf_monitor = get_performance_monitor()
        recent_metrics = perf_monitor.get_recent_metrics(hours)
        
        # Group metrics by function and calculate trends
        function_trends = {}
        
        for metric in recent_metrics:
            func_key = f"{metric.module_name}.{metric.function_name}"
            
            if func_key not in function_trends:
                function_trends[func_key] = {
                    'execution_times': [],
                    'memory_usage': [],
                    'timestamps': [],
                    'error_count': 0
                }
            
            function_trends[func_key]['execution_times'].append(metric.execution_time)
            function_trends[func_key]['memory_usage'].append(
                metric.memory_after_mb - metric.memory_before_mb
            )
            function_trends[func_key]['timestamps'].append(metric.timestamp)
            
            if metric.error:
                function_trends[func_key]['error_count'] += 1
        
        # Calculate trend statistics
        trend_stats = {}
        for func_key, data in function_trends.items():
            if len(data['execution_times']) > 1:
                avg_time = sum(data['execution_times']) / len(data['execution_times'])
                max_time = max(data['execution_times'])
                min_time = min(data['execution_times'])
                avg_memory = sum(data['memory_usage']) / len(data['memory_usage'])
                
                trend_stats[func_key] = {
                    'call_count': len(data['execution_times']),
                    'avg_execution_time': avg_time,
                    'max_execution_time': max_time,
                    'min_execution_time': min_time,
                    'avg_memory_usage': avg_memory,
                    'error_count': data['error_count'],
                    'error_rate': data['error_count'] / len(data['execution_times'])
                }
        
        return jsonify({
            'success': True,
            'data': {
                'trends': trend_stats,
                'time_range_hours': hours,
                'total_metrics': len(recent_metrics)
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@performance_bp.route('/api/alerts')
def get_performance_alerts():
    """Get current performance alerts and recommendations."""
    try:
        perf_monitor = get_performance_monitor()
        health_monitor = get_health_monitor()
        
        # Get critical bottlenecks
        critical_bottlenecks = perf_monitor.get_bottlenecks('critical')
        warning_bottlenecks = perf_monitor.get_bottlenecks('warning')
        
        # Get health status
        health_status = health_monitor.check_health_status()
        
        alerts = []
        
        # Add critical bottleneck alerts
        for bottleneck in critical_bottlenecks:
            alerts.append({
                'type': 'critical',
                'category': 'performance',
                'message': bottleneck.message,
                'function': bottleneck.function_name,
                'timestamp': bottleneck.timestamp,
                'recommendations': bottleneck.recommendations
            })
        
        # Add warning bottleneck alerts
        for bottleneck in warning_bottlenecks:
            alerts.append({
                'type': 'warning',
                'category': 'performance',
                'message': bottleneck.message,
                'function': bottleneck.function_name,
                'timestamp': bottleneck.timestamp,
                'recommendations': bottleneck.recommendations
            })
        
        # Add health alerts
        if health_status.status in ['warning', 'critical']:
            for issue in health_status.issues:
                alerts.append({
                    'type': health_status.status,
                    'category': 'system_health',
                    'message': issue,
                    'timestamp': health_status.last_check,
                    'recommendations': health_status.recommendations
                })
        
        return jsonify({
            'success': True,
            'data': {
                'alerts': alerts,
                'alert_count': len(alerts),
                'critical_count': len([a for a in alerts if a['type'] == 'critical']),
                'warning_count': len([a for a in alerts if a['type'] == 'warning'])
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500
