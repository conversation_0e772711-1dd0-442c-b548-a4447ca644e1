#!/usr/bin/env python3
"""
Test script to verify the time-based greeting fix is working properly.
This script tests the time-based greeting logic with different times.
"""

import sys
import os
import requests
import json
from datetime import datetime, timezone

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_time_determination_backend():
    """Test the backend time determination logic directly."""
    print("Testing backend time determination logic...")
    
    try:
        from app.services.greeting_service import GreetingManager
        
        greeting_manager = GreetingManager()
        
        # Test different time scenarios
        test_cases = [
            # Morning tests
            {'time_of_day': 'morning', 'local_hour': 8, 'expected': 'morning'},
            {'time_of_day': 'morning', 'local_hour': 10, 'expected': 'morning'},
            {'time_of_day': 'morning', 'local_hour': 11, 'expected': 'morning'},
            
            # Afternoon tests  
            {'time_of_day': 'afternoon', 'local_hour': 12, 'expected': 'afternoon'},
            {'time_of_day': 'afternoon', 'local_hour': 15, 'expected': 'afternoon'},
            {'time_of_day': 'afternoon', 'local_hour': 17, 'expected': 'afternoon'},
            
            # Evening tests
            {'time_of_day': 'evening', 'local_hour': 18, 'expected': 'evening'},
            {'time_of_day': 'evening', 'local_hour': 20, 'expected': 'evening'},
            {'time_of_day': 'evening', 'local_hour': 23, 'expected': 'evening'},
        ]
        
        print("  Testing time determination with frontend-provided time_of_day:")
        all_passed = True
        
        for i, test_case in enumerate(test_cases):
            context = {
                'time_of_day': test_case['time_of_day'],
                'local_hour': test_case['local_hour']
            }
            
            result = greeting_manager._determine_time_of_day(context)
            expected = test_case['expected']
            
            if result == expected:
                print(f"    ✓ Test {i+1}: {test_case['time_of_day']} (hour {test_case['local_hour']}) → {result}")
            else:
                print(f"    ✗ Test {i+1}: {test_case['time_of_day']} (hour {test_case['local_hour']}) → {result} (expected {expected})")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"✗ Backend time determination test failed: {str(e)}")
        return False

def test_time_based_greetings():
    """Test time-based greetings for different times of day."""
    print("\nTesting time-based greetings...")
    
    try:
        from app.services.greeting_service import GreetingManager
        
        greeting_manager = GreetingManager()
        
        # Test morning greetings
        print("  Morning greetings:")
        for i in range(3):
            context = {
                'time_of_day': 'morning',
                'local_hour': 10,
                'greeting_type': 'time_based'
            }
            greeting_data = greeting_manager.get_contextual_greeting('TestUser', context)
            print(f"    {i+1}. {greeting_data['greeting']}")
        
        # Test afternoon greetings
        print("\n  Afternoon greetings:")
        for i in range(3):
            context = {
                'time_of_day': 'afternoon', 
                'local_hour': 14,
                'greeting_type': 'time_based'
            }
            greeting_data = greeting_manager.get_contextual_greeting('TestUser', context)
            print(f"    {i+1}. {greeting_data['greeting']}")
        
        # Test evening greetings
        print("\n  Evening greetings:")
        for i in range(3):
            context = {
                'time_of_day': 'evening',
                'local_hour': 20,
                'greeting_type': 'time_based'
            }
            greeting_data = greeting_manager.get_contextual_greeting('TestUser', context)
            print(f"    {i+1}. {greeting_data['greeting']}")
        
        return True
        
    except Exception as e:
        print(f"✗ Time-based greetings test failed: {str(e)}")
        return False

def test_time_based_greeting_api():
    """Test the greeting API with time-based context."""
    print("\nTesting time-based greeting API...")
    
    try:
        # Simulate morning time (10:25 AM)
        morning_time = datetime.now().replace(hour=10, minute=25, second=33)
        
        test_data = {
            'client_name': 'Asta',
            'context': {
                'device_fingerprint': 'test-device-morning',
                'time_of_day': 'morning',
                'local_time': morning_time.isoformat(),
                'local_hour': 10,
                'timezone': 'America/New_York',
                'is_new_session': True,
                'greeting_type': 'time_based'
            }
        }
        
        response = requests.post(
            'http://localhost:5000/api/greeting',
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                greeting = result['greeting']
                print(f"✓ Morning API test successful!")
                print(f"  Time: 10:25 AM")
                print(f"  Greeting: {greeting}")
                
                # Check if it's actually a morning greeting
                if 'morning' in greeting.lower() or 'good morning' in greeting.lower():
                    print(f"  ✓ Correctly identified as morning greeting")
                    return True
                else:
                    print(f"  ✗ Expected morning greeting but got: {greeting}")
                    return False
            else:
                print(f"✗ API returned error: {result.get('error', 'unknown error')}")
                return False
        else:
            print(f"✗ API request failed with status {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("✗ Could not connect to the application. Make sure it's running on localhost:5000")
        return False
    except Exception as e:
        print(f"✗ API test failed: {str(e)}")
        return False

def test_database_time_templates():
    """Test if time-based greeting templates exist in the database."""
    print("\nTesting time-based greeting templates in database...")
    
    try:
        from app.services.greeting_service import GreetingManager
        
        greeting_manager = GreetingManager()
        templates = greeting_manager.get_greeting_templates('time_based')
        
        if templates:
            print(f"✓ Found {len(templates)} time-based greeting templates")
            
            # Group by time of day
            morning_templates = []
            afternoon_templates = []
            evening_templates = []
            
            for template in templates:
                context_conditions = template.get('context_conditions', '{}')
                try:
                    conditions = json.loads(context_conditions) if isinstance(context_conditions, str) else context_conditions
                    time_of_day = conditions.get('time_of_day', 'unknown')
                    
                    if time_of_day == 'morning':
                        morning_templates.append(template)
                    elif time_of_day == 'afternoon':
                        afternoon_templates.append(template)
                    elif time_of_day == 'evening':
                        evening_templates.append(template)
                except:
                    pass
            
            print(f"  Morning templates: {len(morning_templates)}")
            print(f"  Afternoon templates: {len(afternoon_templates)}")
            print(f"  Evening templates: {len(evening_templates)}")
            
            # Show examples
            if morning_templates:
                print(f"  Example morning: {morning_templates[0].get('greeting_text', 'N/A')}")
            if afternoon_templates:
                print(f"  Example afternoon: {afternoon_templates[0].get('greeting_text', 'N/A')}")
            if evening_templates:
                print(f"  Example evening: {evening_templates[0].get('greeting_text', 'N/A')}")
            
            return len(morning_templates) > 0 and len(afternoon_templates) > 0 and len(evening_templates) > 0
        else:
            print("✗ No time-based greeting templates found")
            return False
            
    except Exception as e:
        print(f"✗ Database template test failed: {str(e)}")
        return False

def main():
    """Run all time-based greeting tests."""
    print("=== Time-Based Greeting Fix Test ===\n")
    
    # Test backend time determination
    time_logic_ok = test_time_determination_backend()
    
    # Test database templates
    db_templates_ok = test_database_time_templates()
    
    # Test time-based greetings
    greetings_ok = test_time_based_greetings()
    
    # Test API
    api_ok = test_time_based_greeting_api()
    
    print("\n=== Test Results ===")
    print(f"Time Determination Logic: {'✓ PASS' if time_logic_ok else '✗ FAIL'}")
    print(f"Database Time Templates: {'✓ PASS' if db_templates_ok else '✗ FAIL'}")
    print(f"Time-Based Greetings: {'✓ PASS' if greetings_ok else '✗ FAIL'}")
    print(f"Time-Based API: {'✓ PASS' if api_ok else '✗ FAIL'}")
    
    if time_logic_ok and db_templates_ok and greetings_ok:
        print("\n✓ Time-based greeting system is working correctly!")
        if not api_ok:
            print("⚠ API test failed - make sure the Flask app is running")
        print("\n📝 Testing Instructions:")
        print("1. Start the Flask application")
        print("2. Open the application at 10:25 AM (or any morning time)")
        print("3. Enter your name and check for 'Good morning' greeting")
        print("4. Test at different times of day to verify correct greetings")
    else:
        print("\n✗ Time-based greeting system has issues that need to be fixed")
    
    return time_logic_ok and db_templates_ok and greetings_ok

if __name__ == '__main__':
    main()
