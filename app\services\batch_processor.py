"""
Intelligent Batch PDF Processing Module

This module provides intelligent batch processing capabilities for PDF uploads,
including automatic OCR detection, strategy selection, and optimized processing
for mixed PDF types (OCR and non-OCR).
"""

import os
import tempfile
import shutil
import logging
from typing import List, Dict, Any, Tuple
import fitz  # PyMuPDF

from app.services.pdf_processor import (
    detect_ocr_pdf, convert_pdf_dpi, convert_ocr_to_non_ocr_pdf
)
from app.utils.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)


@performance_monitor(track_memory=True, track_cpu=True, log_parameters=True)
def analyze_batch_pdf_types(files, sample_pages=2):
    """
    Pre-analyze all PDFs in a batch to determine their characteristics.
    
    Args:
        files: List of file objects to analyze
        sample_pages: Number of pages to sample for analysis
        
    Returns:
        dict: Analysis results with recommendations
    """
    logger.info(f"Starting batch analysis for {len(files)} files")
    
    batch_analysis = {
        'total_files': len(files),
        'ocr_files': [],
        'non_ocr_files': [],
        'mixed_files': [],
        'processing_recommendations': [],
        'estimated_processing_time': 0,
        'potential_issues': []
    }
    
    for idx, file in enumerate(files):
        try:
            logger.info(f"Analyzing file {idx + 1}/{len(files)}: {file.filename}")
            
            # Quick OCR detection on sample pages
            ocr_result = detect_ocr_pdf_quick(file, sample_pages)
            
            if ocr_result.get('error'):
                batch_analysis['mixed_files'].append({
                    'index': idx,
                    'filename': file.filename,
                    'error': ocr_result['error']
                })
                batch_analysis['potential_issues'].append(f"File {file.filename}: {ocr_result['error']}")
            elif ocr_result['is_ocr_pdf']:
                batch_analysis['ocr_files'].append({
                    'index': idx,
                    'filename': file.filename,
                    'confidence': ocr_result['confidence'],
                    'text_density': ocr_result['avg_text_per_page'],
                    'text_coverage': ocr_result['text_coverage']
                })
            else:
                batch_analysis['non_ocr_files'].append({
                    'index': idx,
                    'filename': file.filename,
                    'confidence': ocr_result['confidence'],
                    'text_density': ocr_result['avg_text_per_page'],
                    'text_coverage': ocr_result['text_coverage']
                })
                
        except Exception as e:
            logger.error(f"Error analyzing file {file.filename}: {str(e)}")
            batch_analysis['mixed_files'].append({
                'index': idx,
                'filename': file.filename,
                'error': str(e)
            })
            batch_analysis['potential_issues'].append(f"File {file.filename}: Analysis failed - {str(e)}")
    
    # Generate processing recommendations
    batch_analysis['processing_recommendations'] = generate_batch_recommendations(batch_analysis)
    
    # Estimate processing time
    batch_analysis['estimated_processing_time'] = estimate_batch_processing_time(batch_analysis)
    
    logger.info(f"Batch analysis completed: {len(batch_analysis['ocr_files'])} OCR files, "
                f"{len(batch_analysis['non_ocr_files'])} non-OCR files, "
                f"{len(batch_analysis['mixed_files'])} problematic files")
    
    return batch_analysis


def detect_ocr_pdf_quick(file, sample_pages=2):
    """
    Fast OCR detection for batch analysis.
    
    Args:
        file: File object to analyze
        sample_pages: Number of pages to sample
        
    Returns:
        dict: Quick OCR detection results
    """
    try:
        # Create temporary file for analysis
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            file.seek(0)
            shutil.copyfileobj(file, temp_file)
            temp_path = temp_file.name
        
        # Quick analysis
        doc = fitz.open(temp_path)
        pages_to_check = min(sample_pages, len(doc))
        
        total_text = 0
        pages_with_text = 0
        
        for page_num in range(pages_to_check):
            page = doc[page_num]
            text = page.get_text()
            if text.strip():
                total_text += len(text)
                pages_with_text += 1
        
        doc.close()
        os.remove(temp_path)
        
        avg_text = total_text / pages_to_check if pages_to_check > 0 else 0
        text_coverage = pages_with_text / pages_to_check if pages_to_check > 0 else 0
        
        # Adjusted thresholds for quick detection
        is_ocr = avg_text > 200 and text_coverage > 0.2
        confidence = min(0.9, text_coverage * (avg_text / 400))
        
        return {
            'is_ocr_pdf': is_ocr,
            'confidence': confidence,
            'avg_text_per_page': avg_text,
            'text_coverage': text_coverage,
            'pages_analyzed': pages_to_check
        }
        
    except Exception as e:
        logger.error(f"Quick OCR detection failed for {file.filename}: {str(e)}")
        return {
            'is_ocr_pdf': False,
            'confidence': 0.0,
            'error': str(e)
        }


def generate_batch_recommendations(batch_analysis):
    """
    Generate intelligent recommendations based on batch composition.
    
    Args:
        batch_analysis: Results from analyze_batch_pdf_types
        
    Returns:
        list: Processing recommendations
    """
    recommendations = []
    
    total_files = batch_analysis['total_files']
    ocr_count = len(batch_analysis['ocr_files'])
    non_ocr_count = len(batch_analysis['non_ocr_files'])
    mixed_count = len(batch_analysis['mixed_files'])
    
    # OCR-heavy batch recommendations
    if ocr_count > total_files * 0.7:
        recommendations.append("Most files contain OCR text - consider enabling auto-conversion for downloads")
        recommendations.append("High text density detected - DPI conversion may improve processing speed")
        recommendations.append("Enable batch optimization for efficient OCR processing")
    
    # Mixed batch recommendations
    elif ocr_count > 0 and non_ocr_count > 0:
        recommendations.append("Mixed batch detected - individual file processing recommended")
        recommendations.append("Consider enabling batch optimization for efficient processing")
        recommendations.append("OCR files will be processed with text extraction, non-OCR with image analysis")
    
    # Non-OCR heavy batch recommendations
    elif non_ocr_count > total_files * 0.7:
        recommendations.append("Most files are image-based - OCR conversion not needed")
        recommendations.append("Consider higher DPI settings for better image quality")
        recommendations.append("Enable vision model analysis for better image content understanding")
    
    # Performance recommendations
    if total_files > 10:
        recommendations.append("Large batch detected - consider processing in smaller chunks")
        recommendations.append("Enable batch optimization for better performance")
        recommendations.append("Estimated processing time: " + format_processing_time(batch_analysis['estimated_processing_time']))
    
    # Quality recommendations
    if ocr_count > 0:
        avg_confidence = sum(f['confidence'] for f in batch_analysis['ocr_files']) / ocr_count
        if avg_confidence < 0.5:
            recommendations.append("Low OCR confidence detected - manual review may be needed")
        elif avg_confidence > 0.8:
            recommendations.append("High OCR confidence - files should process efficiently")
    
    # Issue warnings
    if mixed_count > 0:
        recommendations.append(f"Warning: {mixed_count} files have analysis issues - they may need manual review")
    
    # General recommendations
    if total_files > 1:
        recommendations.append("Batch processing enabled - files will be processed with optimized strategies")
    
    return recommendations


def estimate_batch_processing_time(batch_analysis):
    """
    Estimate processing time based on batch composition.
    
    Args:
        batch_analysis: Results from analyze_batch_pdf_types
        
    Returns:
        float: Estimated processing time in seconds
    """
    base_time_per_file = 5.0  # Base processing time per file
    ocr_multiplier = 1.5      # OCR files take longer
    non_ocr_multiplier = 1.0  # Non-OCR files standard time
    mixed_multiplier = 2.0    # Problematic files take longer
    
    total_time = 0
    
    # OCR files
    for file_info in batch_analysis['ocr_files']:
        text_density = file_info.get('text_density', 0)
        if text_density > 1000:
            total_time += base_time_per_file * ocr_multiplier * 1.5  # High text density
        else:
            total_time += base_time_per_file * ocr_multiplier
    
    # Non-OCR files
    for file_info in batch_analysis['non_ocr_files']:
        total_time += base_time_per_file * non_ocr_multiplier
    
    # Mixed/problematic files
    for file_info in batch_analysis['mixed_files']:
        total_time += base_time_per_file * mixed_multiplier
    
    return total_time


def format_processing_time(seconds):
    """
    Format processing time in a human-readable format.
    
    Args:
        seconds: Processing time in seconds
        
    Returns:
        str: Formatted time string
    """
    if seconds < 60:
        return f"{seconds:.1f} seconds"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f} minutes"
    else:
        hours = seconds / 3600
        return f"{hours:.1f} hours"


def select_processing_strategy(file_analysis, user_preferences):
    """
    Select the best processing strategy for each file based on analysis and user preferences.
    
    Args:
        file_analysis: Analysis results for a single file
        user_preferences: User-selected processing options
        
    Returns:
        dict: Processing strategy
    """
    strategy = {
        'ocr_conversion': False,
        'non_ocr_to_ocr': False,  # New: Convert non-OCR to OCR for text extraction
        'dpi_conversion': False,
        'target_dpi': user_preferences.get('target_dpi', 300),
        'preserve_original': False,  # Always False - ChromaDB handles search
        'processing_priority': 'normal',
        'use_vision': user_preferences.get('use_vision', False),
        'filter_sensitivity': user_preferences.get('filter_sensitivity', 'medium'),
        'max_images': user_preferences.get('max_images', 10)
    }
    
    # Determine OCR conversion strategy for mixed batch processing
    if user_preferences.get('auto_ocr_conversion', False):
        if file_analysis.get('is_ocr_pdf', False) and file_analysis.get('confidence', 0) > 0.6:
            # OCR PDF: Convert to non-OCR for clean downloads
            strategy['ocr_conversion'] = True
            strategy['preserve_original'] = False  # Always delete original OCR - ChromaDB has the text
        else:
            # Non-OCR PDF: Convert to OCR for text extraction, then back to non-OCR for downloads
            strategy['non_ocr_to_ocr'] = True
            strategy['ocr_conversion'] = True  # Will convert back to non-OCR after text extraction
            strategy['preserve_original'] = False
    
    # Determine DPI conversion strategy
    if user_preferences.get('batch_optimization', False):
        text_density = file_analysis.get('text_density', 0)
        if text_density > 1000:  # High text density
            strategy['dpi_conversion'] = True
            strategy['target_dpi'] = 300  # Standard quality
        elif text_density < 200:  # Low text density
            strategy['dpi_conversion'] = True
            strategy['target_dpi'] = 150  # Lower quality for space savings
    
    # Set processing priority
    confidence = file_analysis.get('confidence', 0)
    if confidence < 0.3:  # Uncertain OCR status
        strategy['processing_priority'] = 'high'  # Process first for manual review
    elif confidence > 0.8:  # High confidence
        strategy['processing_priority'] = 'low'   # Can be processed later
    
    # Adjust vision settings based on file type
    if not file_analysis.get('is_ocr_pdf', False):
        strategy['use_vision'] = True  # Enable vision for non-OCR files
        strategy['max_images'] = min(strategy['max_images'] * 2, 20)  # More images for non-OCR
    
    return strategy


def process_batch_with_intelligence(files, category, user_preferences, source_url=None):
    """
    Process batch with intelligent strategy selection for each file.
    
    Args:
        files: List of file objects to process
        category: Target category
        user_preferences: User processing preferences
        source_url: Optional source URL
        
    Returns:
        tuple: (results, batch_analysis)
    """
    logger.info(f"Starting intelligent batch processing for {len(files)} files")
    
    # Step 1: Analyze all files
    batch_analysis = analyze_batch_pdf_types(files)
    
    # Step 2: Generate processing strategies
    processing_strategies = {}
    for file_info in batch_analysis['ocr_files'] + batch_analysis['non_ocr_files']:
        idx = file_info['index']
        strategy = select_processing_strategy(file_info, user_preferences)
        processing_strategies[idx] = strategy
    
    # Step 3: Process files with their individual strategies
    results = []
    for idx, file in enumerate(files):
        try:
            logger.info(f"Processing file {idx + 1}/{len(files)}: {file.filename}")
            
            strategy = processing_strategies.get(idx, {})
            
            # Apply strategy-specific processing
            if strategy.get('dpi_conversion'):
                # Apply DPI conversion first
                file = apply_dpi_conversion(file, strategy['target_dpi'])
            
            if strategy.get('ocr_conversion'):
                # Apply OCR conversion
                result = process_with_ocr_conversion(file, category, strategy, source_url)
            else:
                # Standard processing
                result = process_standard(file, category, strategy, source_url)
            
            results.append(result)
            
        except Exception as e:
            logger.error(f"Error processing file {file.filename}: {str(e)}")
            results.append({
                'file': file.filename,
                'success': False,
                'message': f"Processing failed: {str(e)}",
                'original_filename': file.filename,
                'system_filename': file.filename,
                'input_index': idx
            })
    
    logger.info(f"Intelligent batch processing completed: {len([r for r in results if r['success']])}/{len(results)} successful")
    
    return results, batch_analysis


def apply_dpi_conversion(file, target_dpi):
    """
    Apply DPI conversion to a file and return a new file wrapper.
    
    Args:
        file: Original file object
        target_dpi: Target DPI for conversion
        
    Returns:
        FileWrapper: New file wrapper with converted PDF
    """
    try:
        # Create temporary file for conversion
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            file.seek(0)
            shutil.copyfileobj(file, temp_file)
            temp_path = temp_file.name
        
        # Convert DPI
        output_path = temp_path.replace('.pdf', f'_dpi{target_dpi}.pdf')
        success, message, metadata = convert_pdf_dpi(temp_path, output_path, target_dpi=target_dpi, preserve_text=True)
        
        if success:
            # Create new file wrapper
            class DPIFileWrapper:
                def __init__(self, file_path, original_filename):
                    self.file_path = file_path
                    self.filename = original_filename
                    self._file_obj = None
                
                def _get_file_obj(self):
                    if self._file_obj is None:
                        self._file_obj = open(self.file_path, 'rb')
                    return self._file_obj
                
                def read(self, *args, **kwargs):
                    return self._get_file_obj().read(*args, **kwargs)
                
                def seek(self, *args, **kwargs):
                    return self._get_file_obj().seek(*args, **kwargs)
                
                def tell(self):
                    return self._get_file_obj().tell()
                
                def close(self):
                    if self._file_obj:
                        self._file_obj.close()
                        self._file_obj = None
                
                def save(self, dest_path):
                    """Save the file to the destination path by copying it"""
                    os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                    shutil.copy2(self.file_path, dest_path)
                
                def __enter__(self):
                    return self
                
                def __exit__(self, exc_type, exc_val, exc_tb):
                    self.close()
                    # Clean up temp files
                    try:
                        if os.path.exists(self.file_path):
                            os.remove(self.file_path)
                        if os.path.exists(temp_path):
                            os.remove(temp_path)
                    except Exception as e:
                        logger.warning(f"Failed to clean up temp files: {e}")
            
            return DPIFileWrapper(output_path, file.filename)
        else:
            logger.warning(f"DPI conversion failed for {file.filename}: {message}")
            return file
            
    except Exception as e:
        logger.error(f"Error applying DPI conversion to {file.filename}: {str(e)}")
        return file


def process_with_ocr_conversion(file, category, strategy, source_url=None):
    """
    Process file with OCR conversion enabled.
    
    Args:
        file: File object to process
        category: Target category
        strategy: Processing strategy
        source_url: Optional source URL
        
    Returns:
        dict: Processing result
    """
    try:
        # Import the upload function from main
        from app.__main__ import upload_regular_pdf_with_ocr_detection
        
        # Check if this is a dual conversion (non-OCR to OCR, then back to non-OCR)
        if strategy.get('non_ocr_to_ocr', False):
            logger.info(f"Processing dual conversion for {file.filename}: non-OCR → OCR → non-OCR")
            
            # Step 1: Convert non-OCR to OCR for text extraction
            temp_ocr_path = apply_non_ocr_to_ocr_conversion(file, strategy['target_dpi'])
            if not temp_ocr_path:
                return {
                    'file': file.filename,
                    'success': False,
                    'message': "Failed to convert non-OCR to OCR for text extraction",
                    'original_filename': file.filename,
                    'system_filename': file.filename,
                    'strategy': strategy
                }
            
            # Step 2: Process the OCR version for text extraction and embedding
            ocr_file_wrapper = create_file_wrapper(temp_ocr_path, file.filename)
            success, message = upload_regular_pdf_with_ocr_detection(
                ocr_file_wrapper, 
                category,
                source_url=source_url,
                use_vision=strategy.get('use_vision', False),
                filter_sensitivity=strategy.get('filter_sensitivity', 'medium'),
                max_images=strategy.get('max_images', 10),
                convert_to_non_ocr=True,  # Convert back to non-OCR for downloads
                conversion_dpi=strategy.get('target_dpi', 300),
                keep_only_non_ocr=True  # Delete OCR version after processing
            )
            
            # Clean up temporary OCR file
            try:
                os.remove(temp_ocr_path)
            except:
                pass
            
        else:
            # Standard OCR to non-OCR conversion
            success, message = upload_regular_pdf_with_ocr_detection(
                file, 
                category,
                source_url=source_url,
                use_vision=strategy.get('use_vision', False),
                filter_sensitivity=strategy.get('filter_sensitivity', 'medium'),
                max_images=strategy.get('max_images', 10),
                convert_to_non_ocr=strategy.get('ocr_conversion', False),
                conversion_dpi=strategy.get('target_dpi', 300),
                keep_only_non_ocr=True  # Always delete original OCR - ChromaDB has the text
            )
        
        return {
            'file': file.filename,
            'success': success,
            'message': message,
            'original_filename': file.filename,
            'system_filename': file.filename,
            'strategy': strategy
        }
        
    except Exception as e:
        logger.error(f"OCR conversion processing failed for {file.filename}: {str(e)}")
        return {
            'file': file.filename,
            'success': False,
            'message': f"OCR conversion failed: {str(e)}",
            'original_filename': file.filename,
            'system_filename': file.filename,
            'strategy': strategy
        }


def apply_non_ocr_to_ocr_conversion(file, target_dpi):
    """
    Convert non-OCR PDF to OCR PDF for text extraction.
    
    Args:
        file: File object to convert
        target_dpi: Target DPI for conversion
        
    Returns:
        str: Path to temporary OCR PDF, or None if failed
    """
    try:
        import tempfile
        from app.services.pdf_processor import convert_non_ocr_to_ocr_pdf
        
        # Create temporary file for OCR version
        temp_dir = tempfile.gettempdir()
        temp_ocr_path = os.path.join(temp_dir, f"temp_ocr_{file.filename}")
        
        # Convert non-OCR to OCR
        success, message, metadata = convert_non_ocr_to_ocr_pdf(
            file.file_path, 
            temp_ocr_path, 
            dpi=target_dpi
        )
        
        if success:
            logger.info(f"Successfully converted {file.filename} to OCR format: {message}")
            return temp_ocr_path
        else:
            logger.error(f"Failed to convert {file.filename} to OCR: {message}")
            return None
            
    except Exception as e:
        logger.error(f"Error in non-OCR to OCR conversion for {file.filename}: {str(e)}")
        return None


def create_file_wrapper(file_path, original_filename):
    """
    Create a file wrapper for the converted file.
    
    Args:
        file_path: Path to the file
        original_filename: Original filename
        
    Returns:
        FileWrapper: File wrapper object
    """
    class FileWrapper:
        def __init__(self, file_path, original_filename):
            self.file_path = file_path
            self.filename = original_filename
            self._file_obj = None

        def _get_file_obj(self):
            if self._file_obj is None:
                self._file_obj = open(self.file_path, 'rb')
            return self._file_obj

        def read(self, *args, **kwargs):
            return self._get_file_obj().read(*args, **kwargs)

        def seek(self, *args, **kwargs):
            return self._get_file_obj().seek(*args, **kwargs)

        def tell(self):
            return self._get_file_obj().tell()

        def close(self):
            if self._file_obj:
                self._file_obj.close()
                self._file_obj = None

        def save(self, dest_path):
            """Save the file to the destination path by copying it"""
            import shutil
            os.makedirs(os.path.dirname(dest_path), exist_ok=True)
            shutil.copy2(self.file_path, dest_path)
    
    return FileWrapper(file_path, original_filename)


def process_standard(file, category, strategy, source_url=None):
    """
    Process file with standard processing (no OCR conversion).
    
    Args:
        file: File object to process
        category: Target category
        strategy: Processing strategy
        source_url: Optional source URL
        
    Returns:
        dict: Processing result
    """
    try:
        # Import the upload function from main
        from app.__main__ import upload_regular_pdf_with_ocr_detection
        
        # Use the existing upload function without OCR conversion
        success, message = upload_regular_pdf_with_ocr_detection(
            file, 
            category,
            source_url=source_url,
            use_vision=strategy.get('use_vision', False),
            filter_sensitivity=strategy.get('filter_sensitivity', 'medium'),
            max_images=strategy.get('max_images', 10),
            convert_to_non_ocr=False,  # No OCR conversion
            conversion_dpi=strategy.get('target_dpi', 300),
            keep_only_non_ocr=False
        )
        
        return {
            'file': file.filename,
            'success': success,
            'message': message,
            'original_filename': file.filename,
            'system_filename': file.filename,
            'strategy': strategy
        }
        
    except Exception as e:
        logger.error(f"Standard processing failed for {file.filename}: {str(e)}")
        return {
            'file': file.filename,
            'success': False,
            'message': f"Standard processing failed: {str(e)}",
            'original_filename': file.filename,
            'system_filename': file.filename,
            'strategy': strategy
        } 