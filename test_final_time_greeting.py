#!/usr/bin/env python3
"""
Final test to verify the complete time-based greeting fix.
This simulates the exact scenario: 10:25 AM should show "Good morning" greeting.
"""

import sys
import os
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_exact_scenario():
    """Test the exact scenario: 10:25 AM should show Good morning greeting."""
    print("=== Testing Exact Scenario: 10:25 AM ===\n")
    
    try:
        from app.services.greeting_service import GreetingManager
        
        greeting_manager = GreetingManager()
        
        # Simulate the exact time: 10:25:33 AM
        morning_time = datetime.now().replace(hour=10, minute=25, second=33)
        
        # Test context exactly as frontend would send it
        context = {
            'time_of_day': 'morning',  # Frontend calculated
            'local_time': morning_time.isoformat(),
            'local_hour': 10,
            'timezone': 'America/New_York',
            'device_fingerprint': 'test-device-asta',
            'is_new_session': True,
            'greeting_type': 'time_based'
        }
        
        print(f"Testing with context:")
        print(f"  Time: {morning_time.strftime('%I:%M:%S %p')}")
        print(f"  time_of_day: {context['time_of_day']}")
        print(f"  local_hour: {context['local_hour']}")
        print()
        
        # Test multiple greetings to see variation
        print("Generated greetings:")
        morning_greetings = []
        
        for i in range(5):
            greeting_data = greeting_manager.get_contextual_greeting('Asta', context)
            greeting = greeting_data['greeting']
            morning_greetings.append(greeting)
            
            # Check if it's a proper morning greeting
            is_morning_greeting = any(word in greeting.lower() for word in ['morning', 'good morning'])
            status = "✓" if is_morning_greeting else "✗"
            
            print(f"  {i+1}. {status} {greeting}")
        
        # Verify all greetings are morning greetings
        morning_count = sum(1 for g in morning_greetings if any(word in g.lower() for word in ['morning', 'good morning']))
        
        print(f"\nResults:")
        print(f"  Total greetings tested: {len(morning_greetings)}")
        print(f"  Morning greetings: {morning_count}")
        print(f"  Success rate: {morning_count/len(morning_greetings)*100:.1f}%")
        
        if morning_count >= len(morning_greetings) * 0.8:  # Allow for some variation
            print(f"  ✓ SUCCESS: Correctly showing morning greetings at 10:25 AM")
            return True
        else:
            print(f"  ✗ FAILURE: Not showing enough morning greetings")
            return False
            
    except Exception as e:
        print(f"✗ Test failed with error: {str(e)}")
        return False

def test_time_boundaries():
    """Test the time boundaries to ensure correct transitions."""
    print("\n=== Testing Time Boundaries ===\n")
    
    try:
        from app.services.greeting_service import GreetingManager
        
        greeting_manager = GreetingManager()
        
        # Test boundary times
        test_times = [
            (11, 59, 'morning'),   # Just before noon
            (12, 0, 'afternoon'),  # Exactly noon
            (12, 1, 'afternoon'),  # Just after noon
            (17, 59, 'afternoon'), # Just before 6 PM
            (18, 0, 'evening'),    # Exactly 6 PM
            (18, 1, 'evening'),    # Just after 6 PM
        ]
        
        all_passed = True
        
        for hour, minute, expected_time in test_times:
            context = {
                'time_of_day': expected_time,
                'local_hour': hour,
                'greeting_type': 'time_based'
            }
            
            greeting_data = greeting_manager.get_contextual_greeting('TestUser', context)
            greeting = greeting_data['greeting']
            
            # Check if greeting matches expected time
            time_words = {
                'morning': ['morning', 'good morning'],
                'afternoon': ['afternoon', 'good afternoon'],
                'evening': ['evening', 'good evening']
            }
            
            expected_words = time_words[expected_time]
            has_correct_time = any(word in greeting.lower() for word in expected_words)
            
            status = "✓" if has_correct_time else "✗"
            time_str = f"{hour:02d}:{minute:02d}"
            
            print(f"  {status} {time_str} ({expected_time}): {greeting}")
            
            if not has_correct_time:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"✗ Boundary test failed: {str(e)}")
        return False

def test_frontend_time_calculation():
    """Test that frontend time calculation matches backend expectations."""
    print("\n=== Testing Frontend Time Calculation ===\n")
    
    # Simulate the frontend getTimeOfDay() function
    def frontend_getTimeOfDay(hour):
        if hour < 12:
            return 'morning'
        elif hour < 18:
            return 'afternoon'
        else:
            return 'evening'
    
    # Test various hours
    test_hours = [6, 10, 11, 12, 15, 17, 18, 20, 23]
    
    print("Frontend time calculation:")
    for hour in test_hours:
        time_of_day = frontend_getTimeOfDay(hour)
        time_str = f"{hour:02d}:00"
        print(f"  {time_str} → {time_of_day}")
    
    # Test the specific case: 10:25 AM
    specific_hour = 10
    specific_time_of_day = frontend_getTimeOfDay(specific_hour)
    
    print(f"\nSpecific case:")
    print(f"  10:25 AM → {specific_time_of_day}")
    
    if specific_time_of_day == 'morning':
        print(f"  ✓ Frontend correctly identifies 10:25 AM as morning")
        return True
    else:
        print(f"  ✗ Frontend incorrectly identifies 10:25 AM as {specific_time_of_day}")
        return False

def main():
    """Run all final tests."""
    print("=== Final Time-Based Greeting Test ===")
    print("Testing the fix for: 10:25 AM showing 'Good afternoon' instead of 'Good morning'\n")
    
    # Test the exact scenario
    scenario_ok = test_exact_scenario()
    
    # Test time boundaries
    boundaries_ok = test_time_boundaries()
    
    # Test frontend calculation
    frontend_ok = test_frontend_time_calculation()
    
    print("\n" + "="*50)
    print("FINAL RESULTS")
    print("="*50)
    print(f"Exact Scenario (10:25 AM): {'✓ PASS' if scenario_ok else '✗ FAIL'}")
    print(f"Time Boundaries: {'✓ PASS' if boundaries_ok else '✗ FAIL'}")
    print(f"Frontend Calculation: {'✓ PASS' if frontend_ok else '✗ FAIL'}")
    
    if scenario_ok and boundaries_ok and frontend_ok:
        print(f"\n🎉 SUCCESS: Time-based greeting fix is working correctly!")
        print(f"   At 10:25 AM, users will now see 'Good morning' greetings.")
        print(f"\n📋 Next Steps:")
        print(f"   1. Start the Flask application")
        print(f"   2. Test at 10:25 AM (or any morning time)")
        print(f"   3. Verify you see 'Good morning, [name]!' instead of 'Good afternoon'")
    else:
        print(f"\n❌ FAILURE: Time-based greeting fix needs more work")
    
    return scenario_ok and boundaries_ok and frontend_ok

if __name__ == '__main__':
    main()
