"""
Content Type Detection for Adaptive Text Chunking
Analyzes document content to determine optimal chunking strategy.
"""

import re
import logging
from typing import Dict, List, Tuple
from collections import Counter

logger = logging.getLogger(__name__)

class ContentTypeDetector:
    """Detects content type for adaptive chunking strategies"""
    
    def __init__(self):
        # Technical content indicators
        self.technical_keywords = [
            'algorithm', 'function', 'method', 'implementation', 'framework',
            'architecture', 'system', 'protocol', 'interface', 'api',
            'database', 'query', 'optimization', 'performance', 'scalability',
            'configuration', 'deployment', 'infrastructure', 'security',
            'authentication', 'authorization', 'encryption', 'validation'
        ]
        
        # Scientific content indicators
        self.scientific_keywords = [
            'hypothesis', 'experiment', 'methodology', 'analysis', 'results',
            'conclusion', 'research', 'study', 'investigation', 'observation',
            'measurement', 'data', 'statistical', 'significant', 'correlation',
            'variable', 'control', 'sample', 'population', 'distribution',
            'regression', 'model', 'theory', 'evidence', 'peer-reviewed'
        ]
        
        # Narrative content indicators
        self.narrative_keywords = [
            'story', 'narrative', 'chapter', 'character', 'plot', 'dialogue',
            'setting', 'theme', 'protagonist', 'antagonist', 'conflict',
            'resolution', 'climax', 'exposition', 'development', 'conclusion',
            'once upon', 'meanwhile', 'suddenly', 'finally', 'in the end'
        ]
        
        # Academic/formal content indicators
        self.academic_keywords = [
            'abstract', 'introduction', 'literature review', 'methodology',
            'findings', 'discussion', 'references', 'bibliography', 'citation',
            'furthermore', 'however', 'therefore', 'consequently', 'moreover',
            'nevertheless', 'in contrast', 'similarly', 'specifically'
        ]
        
        # Patterns for different content types
        self.technical_patterns = [
            r'\b(?:def|class|function|import|from)\s+\w+',  # Code patterns
            r'\b(?:GET|POST|PUT|DELETE)\s+/',  # API endpoints
            r'\b(?:SELECT|INSERT|UPDATE|DELETE)\s+',  # SQL patterns
            r'[a-zA-Z_][a-zA-Z0-9_]*\(\)',  # Function calls
            r'\b(?:http|https|ftp)://\S+',  # URLs
        ]
        
        self.scientific_patterns = [
            r'\bp\s*[<>=]\s*0\.\d+',  # Statistical significance
            r'\b(?:n|N)\s*=\s*\d+',  # Sample size notation
            r'\b\d+\.\d+\s*±\s*\d+\.\d+',  # Error margins
            r'\b(?:Figure|Table|Equation)\s+\d+',  # Reference patterns
            r'\b(?:et al\.|ibid\.|op\. cit\.)',  # Academic citations
        ]
        
        self.narrative_patterns = [
            r'"[^"]*"',  # Dialogue
            r'\b(?:he|she|they)\s+(?:said|asked|replied|whispered)',  # Speech tags
            r'\b(?:Chapter|Part)\s+\d+',  # Chapter markers
            r'\b(?:Once|Long ago|In the beginning)',  # Story starters
        ]
    
    def detect_content_type(self, text_sample: str, min_sample_length: int = 500) -> str:
        """
        Detect content type based on text analysis
        
        Args:
            text_sample: Sample text to analyze (first 1000-2000 chars recommended)
            min_sample_length: Minimum length required for reliable detection
            
        Returns:
            Content type: 'technical', 'scientific', 'narrative', 'academic', or 'general'
        """
        if len(text_sample) < min_sample_length:
            logger.warning(f"Text sample too short ({len(text_sample)} chars) for reliable detection")
            return "general"
        
        # Normalize text for analysis
        text_lower = text_sample.lower()
        
        # Calculate keyword scores
        scores = self._calculate_keyword_scores(text_lower)
        
        # Calculate pattern scores
        pattern_scores = self._calculate_pattern_scores(text_sample)
        
        # Combine scores
        combined_scores = {
            'technical': scores['technical'] + pattern_scores['technical'],
            'scientific': scores['scientific'] + pattern_scores['scientific'],
            'narrative': scores['narrative'] + pattern_scores['narrative'],
            'academic': scores['academic']
        }
        
        # Determine content type based on highest score
        max_score_type = max(combined_scores, key=combined_scores.get)
        max_score = combined_scores[max_score_type]
        
        # Require minimum threshold for classification
        min_threshold = 3
        if max_score < min_threshold:
            return "general"
        
        # Special case: if academic score is high, check for scientific content
        if max_score_type == 'academic' and combined_scores['scientific'] > min_threshold:
            return "scientific"
        
        logger.info(f"Detected content type: {max_score_type} (score: {max_score})")
        return max_score_type if max_score_type != 'academic' else 'general'
    
    def _calculate_keyword_scores(self, text_lower: str) -> Dict[str, int]:
        """Calculate scores based on keyword frequency"""
        scores = {
            'technical': sum(1 for keyword in self.technical_keywords if keyword in text_lower),
            'scientific': sum(1 for keyword in self.scientific_keywords if keyword in text_lower),
            'narrative': sum(1 for keyword in self.narrative_keywords if keyword in text_lower),
            'academic': sum(1 for keyword in self.academic_keywords if keyword in text_lower)
        }
        return scores
    
    def _calculate_pattern_scores(self, text: str) -> Dict[str, int]:
        """Calculate scores based on regex pattern matches"""
        scores = {
            'technical': sum(len(re.findall(pattern, text, re.IGNORECASE)) 
                           for pattern in self.technical_patterns),
            'scientific': sum(len(re.findall(pattern, text, re.IGNORECASE)) 
                            for pattern in self.scientific_patterns),
            'narrative': sum(len(re.findall(pattern, text, re.IGNORECASE)) 
                           for pattern in self.narrative_patterns)
        }
        return scores
    
    def analyze_document_structure(self, text: str) -> Dict[str, any]:
        """
        Analyze document structure for additional insights
        
        Returns:
            Dictionary with structural analysis results
        """
        lines = text.split('\n')
        paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
        
        # Calculate metrics
        avg_line_length = sum(len(line) for line in lines) / len(lines) if lines else 0
        avg_paragraph_length = sum(len(p) for p in paragraphs) / len(paragraphs) if paragraphs else 0
        
        # Count structural elements
        headers = len(re.findall(r'^#+\s+', text, re.MULTILINE))  # Markdown headers
        bullet_points = len(re.findall(r'^\s*[-*+]\s+', text, re.MULTILINE))
        numbered_lists = len(re.findall(r'^\s*\d+\.\s+', text, re.MULTILINE))
        
        return {
            'total_lines': len(lines),
            'total_paragraphs': len(paragraphs),
            'avg_line_length': avg_line_length,
            'avg_paragraph_length': avg_paragraph_length,
            'headers_count': headers,
            'bullet_points_count': bullet_points,
            'numbered_lists_count': numbered_lists,
            'has_structured_content': headers > 0 or bullet_points > 0 or numbered_lists > 0
        }
    
    def get_recommended_strategy(self, content_type: str, structure_analysis: Dict = None) -> str:
        """
        Get recommended chunking strategy based on content type and structure
        
        Args:
            content_type: Detected content type
            structure_analysis: Optional structure analysis results
            
        Returns:
            Recommended strategy: 'semantic', 'sentence_aware', or 'character_based'
        """
        if content_type in ['technical', 'scientific']:
            return 'semantic'
        elif content_type == 'narrative':
            return 'sentence_aware'
        elif structure_analysis and structure_analysis.get('has_structured_content', False):
            return 'sentence_aware'
        else:
            return 'sentence_aware'  # Default to sentence-aware for better boundary detection
